# Local build version of docker-compose for when Docker Hub is not accessible
# This builds the main Dify services locally and uses minimal external dependencies

x-shared-env: &shared-api-worker-env
  CONSOLE_API_URL: ${CONSOLE_API_URL:-}
  CONSOLE_WEB_URL: ${CONSOLE_WEB_URL:-}
  SERVICE_API_URL: ${SERVICE_API_URL:-}
  APP_API_URL: ${APP_API_URL:-}
  APP_WEB_URL: ${APP_WEB_URL:-}
  FILES_URL: ${FILES_URL:-}
  LOG_LEVEL: ${LOG_LEVEL:-INFO}
  LOG_FILE: ${LOG_FILE:-/app/logs/server.log}
  LOG_FILE_MAX_SIZE: ${LOG_FILE_MAX_SIZE:-20}
  LOG_FILE_BACKUP_COUNT: ${LOG_FILE_BACKUP_COUNT:-5}
  LOG_DATEFORMAT: ${LOG_DATEFORMAT:-%Y-%m-%d %H:%M:%S}
  LOG_TZ: ${LOG_TZ:-UTC}
  DEBUG: ${DEBUG:-false}
  FLASK_DEBUG: ${FLASK_DEBUG:-false}
  ENABLE_REQUEST_LOGGING: ${ENABLE_REQUEST_LOGGING:-False}
  SECRET_KEY: ${SECRET_KEY:-************************************************}
  INIT_PASSWORD: ${INIT_PASSWORD:-}
  DEPLOY_ENV: ${DEPLOY_ENV:-PRODUCTION}
  CHECK_UPDATE_URL: ${CHECK_UPDATE_URL:-https://updates.dify.ai}
  OPENAI_API_BASE: ${OPENAI_API_BASE:-https://api.openai.com/v1}
  MIGRATION_ENABLED: ${MIGRATION_ENABLED:-true}
  FILES_ACCESS_TIMEOUT: ${FILES_ACCESS_TIMEOUT:-300}
  ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-60}
  REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-30}
  APP_MAX_ACTIVE_REQUESTS: ${APP_MAX_ACTIVE_REQUESTS:-0}
  APP_MAX_EXECUTION_TIME: ${APP_MAX_EXECUTION_TIME:-1200}
  DIFY_BIND_ADDRESS: ${DIFY_BIND_ADDRESS:-0.0.0.0}
  DIFY_PORT: ${DIFY_PORT:-5001}
  SERVER_WORKER_AMOUNT: ${SERVER_WORKER_AMOUNT:-1}
  SERVER_WORKER_CLASS: ${SERVER_WORKER_CLASS:-gevent}
  SERVER_WORKER_CONNECTIONS: ${SERVER_WORKER_CONNECTIONS:-10}
  CELERY_WORKER_CLASS: ${CELERY_WORKER_CLASS:-}
  GUNICORN_TIMEOUT: ${GUNICORN_TIMEOUT:-360}
  CELERY_WORKER_AMOUNT: ${CELERY_WORKER_AMOUNT:-}
  CELERY_AUTO_SCALE: ${CELERY_AUTO_SCALE:-false}
  CELERY_MAX_WORKERS: ${CELERY_MAX_WORKERS:-}
  CELERY_MIN_WORKERS: ${CELERY_MIN_WORKERS:-}
  API_TOOL_DEFAULT_CONNECT_TIMEOUT: ${API_TOOL_DEFAULT_CONNECT_TIMEOUT:-10}
  API_TOOL_DEFAULT_READ_TIMEOUT: ${API_TOOL_DEFAULT_READ_TIMEOUT:-60}
  ENABLE_WEBSITE_JINAREADER: ${ENABLE_WEBSITE_JINAREADER:-true}
  ENABLE_WEBSITE_FIRECRAWL: ${ENABLE_WEBSITE_FIRECRAWL:-true}
  ENABLE_WEBSITE_WATERCRAWL: ${ENABLE_WEBSITE_WATERCRAWL:-true}
  DB_USERNAME: ${DB_USERNAME:-postgres}
  DB_PASSWORD: ${DB_PASSWORD:-difyai123456}
  DB_HOST: ${DB_HOST:-db}
  DB_PORT: ${DB_PORT:-5432}
  DB_DATABASE: ${DB_DATABASE:-dify}
  REDIS_HOST: ${REDIS_HOST:-redis}
  REDIS_PORT: ${REDIS_PORT:-6379}
  REDIS_PASSWORD: ${REDIS_PASSWORD:-difyai123456}
  REDIS_USE_SSL: ${REDIS_USE_SSL:-false}
  REDIS_DB: ${REDIS_DB:-0}
  CELERY_BROKER_URL: ${CELERY_BROKER_URL:-redis://:difyai123456@redis:6379/1}
  BROKER_USE_SSL: ${BROKER_USE_SSL:-false}
  WEB_API_CORS_ALLOW_ORIGINS: ${WEB_API_CORS_ALLOW_ORIGINS:-*}
  CONSOLE_CORS_ALLOW_ORIGINS: ${CONSOLE_CORS_ALLOW_ORIGINS:-*}
  STORAGE_TYPE: ${STORAGE_TYPE:-local}
  STORAGE_LOCAL_PATH: ${STORAGE_LOCAL_PATH:-storage}
  VECTOR_STORE: ${VECTOR_STORE:-weaviate}
  WEAVIATE_ENDPOINT: ${WEAVIATE_ENDPOINT:-http://weaviate:8080}
  WEAVIATE_API_KEY: ${WEAVIATE_API_KEY:-WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih}
  UPLOAD_FILE_SIZE_LIMIT: ${UPLOAD_FILE_SIZE_LIMIT:-15}
  UPLOAD_FILE_BATCH_LIMIT: ${UPLOAD_FILE_BATCH_LIMIT:-5}
  UPLOAD_IMAGE_FILE_SIZE_LIMIT: ${UPLOAD_IMAGE_FILE_SIZE_LIMIT:-10}
  MULTIMODAL_SEND_IMAGE_FORMAT: ${MULTIMODAL_SEND_IMAGE_FORMAT:-base64}
  SENTRY_TRACES_SAMPLE_RATE: ${SENTRY_TRACES_SAMPLE_RATE:-1.0}
  SENTRY_PROFILES_SAMPLE_RATE: ${SENTRY_PROFILES_SAMPLE_RATE:-1.0}
  NOTION_INTEGRATION_TYPE: ${NOTION_INTEGRATION_TYPE:-public}
  NOTION_CLIENT_SECRET: ${NOTION_CLIENT_SECRET:-}
  NOTION_CLIENT_ID: ${NOTION_CLIENT_ID:-}
  NOTION_INTERNAL_SECRET: ${NOTION_INTERNAL_SECRET:-}
  MAIL_TYPE: ${MAIL_TYPE:-}
  MAIL_DEFAULT_SEND_FROM: ${MAIL_DEFAULT_SEND_FROM:-}
  SMTP_SERVER: ${SMTP_SERVER:-}
  SMTP_PORT: ${SMTP_PORT:-587}
  SMTP_USERNAME: ${SMTP_USERNAME:-}
  SMTP_PASSWORD: ${SMTP_PASSWORD:-}
  SMTP_USE_TLS: ${SMTP_USE_TLS:-false}
  SMTP_OPPORTUNISTIC_TLS: ${SMTP_OPPORTUNISTIC_TLS:-false}
  RESEND_API_KEY: ${RESEND_API_KEY:-}
  RESEND_API_URL: ${RESEND_API_URL:-https://api.resend.com}
  INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH: ${INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH:-1000}
  BATCH_UPLOAD_LIMIT: ${BATCH_UPLOAD_LIMIT:-20}
  ETL_TYPE: ${ETL_TYPE:-dify}
  UNSTRUCTURED_API_URL: ${UNSTRUCTURED_API_URL:-}
  UNSTRUCTURED_API_KEY: ${UNSTRUCTURED_API_KEY:-}
  POSITION_TOOL_PINS_USER: ${POSITION_TOOL_PINS_USER:-}
  POSITION_TOOL_PINS_ASSISTANT: ${POSITION_TOOL_PINS_ASSISTANT:-}
  POSITION_TOOL_PINS_SYSTEM: ${POSITION_TOOL_PINS_SYSTEM:-}
  INVITE_EXPIRY_HOURS: ${INVITE_EXPIRY_HOURS:-72}
  RESET_PASSWORD_TOKEN_EXPIRY_HOURS: ${RESET_PASSWORD_TOKEN_EXPIRY_HOURS:-24}
  CODE_EXECUTION_ENDPOINT: ${CODE_EXECUTION_ENDPOINT:-http://sandbox:8194}
  CODE_EXECUTION_API_KEY: ${CODE_EXECUTION_API_KEY:-dify-sandbox}
  CODE_MAX_NUMBER: ${CODE_MAX_NUMBER:-9223372036854775807}
  CODE_MIN_NUMBER: ${CODE_MIN_NUMBER:--9223372036854775808}
  CODE_MAX_DEPTH: ${CODE_MAX_DEPTH:-5}
  CODE_MAX_PRECISION: ${CODE_MAX_PRECISION:-20}
  CODE_MAX_STRING_LENGTH: ${CODE_MAX_STRING_LENGTH:-80000}
  TEMPLATE_TRANSFORM_MAX_LENGTH: ${TEMPLATE_TRANSFORM_MAX_LENGTH:-80000}
  CODE_MAX_STRING_ARRAY_LENGTH: ${CODE_MAX_STRING_ARRAY_LENGTH:-30}
  CODE_MAX_OBJECT_ARRAY_LENGTH: ${CODE_MAX_OBJECT_ARRAY_LENGTH:-30}
  CODE_MAX_NUMBER_ARRAY_LENGTH: ${CODE_MAX_NUMBER_ARRAY_LENGTH:-1000}
  WORKFLOW_MAX_EXECUTION_STEPS: ${WORKFLOW_MAX_EXECUTION_STEPS:-500}
  WORKFLOW_MAX_EXECUTION_TIME: ${WORKFLOW_MAX_EXECUTION_TIME:-1200}
  WORKFLOW_CALL_MAX_DEPTH: ${WORKFLOW_CALL_MAX_DEPTH:-5}
  HTTP_REQUEST_MAX_CONNECT_TIMEOUT: ${HTTP_REQUEST_MAX_CONNECT_TIMEOUT:-10}
  HTTP_REQUEST_MAX_READ_TIMEOUT: ${HTTP_REQUEST_MAX_READ_TIMEOUT:-60}
  HTTP_REQUEST_MAX_WRITE_TIMEOUT: ${HTTP_REQUEST_MAX_WRITE_TIMEOUT:-10}
  HTTP_REQUEST_NODE_MAX_BINARY_SIZE: ${HTTP_REQUEST_NODE_MAX_BINARY_SIZE:-10485760}
  HTTP_REQUEST_NODE_MAX_TEXT_SIZE: ${HTTP_REQUEST_NODE_MAX_TEXT_SIZE:-1048576}
  VARIABLE_MAX_STRING_LENGTH: ${VARIABLE_MAX_STRING_LENGTH:-200000}
  CONVERSATION_MAX_HISTORY_MESSAGES: ${CONVERSATION_MAX_HISTORY_MESSAGES:-1000}
  MAIL_TYPE_RESEND: ${MAIL_TYPE_RESEND:-resend}
  MAIL_TYPE_SMTP: ${MAIL_TYPE_SMTP:-smtp}
  CLEAN_DAY_SETTING: ${CLEAN_DAY_SETTING:-30}
  UPLOAD_FILE_EXTENSION_WHITELIST: ${UPLOAD_FILE_EXTENSION_WHITELIST:-}
  MODERATION_BUFFER_SIZE: ${MODERATION_BUFFER_SIZE:-300}
  MAX_VARIABLE_SIZE: ${MAX_VARIABLE_SIZE:-200000}
  BILLING_ENABLED: ${BILLING_ENABLED:-false}
  CAN_REPLACE_LOGO: ${CAN_REPLACE_LOGO:-false}
  MODEL_LB_ENABLED: ${MODEL_LB_ENABLED:-false}
  ENTERPRISE_ENABLED: ${ENTERPRISE_ENABLED:-false}
  HOSTED_OPENAI_QUOTA_LIMIT: ${HOSTED_OPENAI_QUOTA_LIMIT:-200}
  HOSTED_OPENAI_PAID_INCREASE_QUOTA: ${HOSTED_OPENAI_PAID_INCREASE_QUOTA:-1}
  HOSTED_OPENAI_PAID_MIN_QUOTA: ${HOSTED_OPENAI_PAID_MIN_QUOTA:-20}
  HOSTED_OPENAI_PAID_MAX_QUOTA: ${HOSTED_OPENAI_PAID_MAX_QUOTA:-200}
  STRIPE_PUBLISHABLE_KEY: ${STRIPE_PUBLISHABLE_KEY:-}
  STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY:-}
  STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET:-}
  STRIPE_PRICE_ID: ${STRIPE_PRICE_ID:-}
  HOSTED_ANTHROPIC_QUOTA_LIMIT: ${HOSTED_ANTHROPIC_QUOTA_LIMIT:-600000}
  HOSTED_ANTHROPIC_PAID_INCREASE_QUOTA: ${HOSTED_ANTHROPIC_PAID_INCREASE_QUOTA:-1000000}
  HOSTED_ANTHROPIC_PAID_MIN_QUOTA: ${HOSTED_ANTHROPIC_PAID_MIN_QUOTA:-600000}
  HOSTED_ANTHROPIC_PAID_MAX_QUOTA: ${HOSTED_ANTHROPIC_PAID_MAX_QUOTA:-5000000}
  HOSTED_MINIMAX_ENABLED: ${HOSTED_MINIMAX_ENABLED:-false}
  HOSTED_SPARK_ENABLED: ${HOSTED_SPARK_ENABLED:-false}
  HOSTED_ZHIPUAI_ENABLED: ${HOSTED_ZHIPUAI_ENABLED:-false}
  HOSTED_MODERATION_ENABLED: ${HOSTED_MODERATION_ENABLED:-false}
  HOSTED_MODERATION_PROVIDERS: ${HOSTED_MODERATION_PROVIDERS:-}
  HOSTED_FETCH_APP_TEMPLATES_MODE: ${HOSTED_FETCH_APP_TEMPLATES_MODE:-remote}
  HOSTED_FETCH_APP_TEMPLATES_REMOTE_DOMAIN: ${HOSTED_FETCH_APP_TEMPLATES_REMOTE_DOMAIN:-https://tmpl.dify.ai}
  ACCOUNT_REGISTRATION_ENABLED: ${ACCOUNT_REGISTRATION_ENABLED:-true}
  SOCIAL_OAUTH_ENABLED: ${SOCIAL_OAUTH_ENABLED:-false}
  SOCIAL_GITHUB_CLIENT_ID: ${SOCIAL_GITHUB_CLIENT_ID:-}
  SOCIAL_GITHUB_CLIENT_SECRET: ${SOCIAL_GITHUB_CLIENT_SECRET:-}
  SOCIAL_GOOGLE_CLIENT_ID: ${SOCIAL_GOOGLE_CLIENT_ID:-}
  SOCIAL_GOOGLE_CLIENT_SECRET: ${SOCIAL_GOOGLE_CLIENT_SECRET:-}
  SOCIAL_DINGTALK_CLIENT_ID: ${SOCIAL_DINGTALK_CLIENT_ID:-}
  SOCIAL_DINGTALK_CLIENT_SECRET: ${SOCIAL_DINGTALK_CLIENT_SECRET:-}
  SOCIAL_LARK_CLIENT_ID: ${SOCIAL_LARK_CLIENT_ID:-}
  SOCIAL_LARK_CLIENT_SECRET: ${SOCIAL_LARK_CLIENT_SECRET:-}
  SOCIAL_OIDC_CLIENT_ID: ${SOCIAL_OIDC_CLIENT_ID:-}
  SOCIAL_OIDC_CLIENT_SECRET: ${SOCIAL_OIDC_CLIENT_SECRET:-}
  SOCIAL_OIDC_ISSUER: ${SOCIAL_OIDC_ISSUER:-}
  SOCIAL_OIDC_AUTHORIZATION_ENDPOINT: ${SOCIAL_OIDC_AUTHORIZATION_ENDPOINT:-}
  SOCIAL_OIDC_TOKEN_ENDPOINT: ${SOCIAL_OIDC_TOKEN_ENDPOINT:-}
  SOCIAL_OIDC_USERINFO_ENDPOINT: ${SOCIAL_OIDC_USERINFO_ENDPOINT:-}
  SOCIAL_OIDC_JWKS_URI: ${SOCIAL_OIDC_JWKS_URI:-}
  SOCIAL_OIDC_SCOPE: ${SOCIAL_OIDC_SCOPE:-openid profile email}
  SOCIAL_OIDC_USERNAME_CLAIM: ${SOCIAL_OIDC_USERNAME_CLAIM:-preferred_username}
  SOCIAL_OIDC_EMAIL_CLAIM: ${SOCIAL_OIDC_EMAIL_CLAIM:-email}
  SOCIAL_OIDC_DISPLAY_NAME_CLAIM: ${SOCIAL_OIDC_DISPLAY_NAME_CLAIM:-name}
  SOCIAL_OIDC_PICTURE_CLAIM: ${SOCIAL_OIDC_PICTURE_CLAIM:-picture}
  SOCIAL_OIDC_GROUPS_CLAIM: ${SOCIAL_OIDC_GROUPS_CLAIM:-groups}
  SOCIAL_OIDC_GROUPS_REQUIRED: ${SOCIAL_OIDC_GROUPS_REQUIRED:-}
  SOCIAL_OIDC_GROUPS_ADMIN: ${SOCIAL_OIDC_GROUPS_ADMIN:-}
  SOCIAL_OIDC_GROUPS_EDITOR: ${SOCIAL_OIDC_GROUPS_EDITOR:-}
  SOCIAL_OIDC_GROUPS_DATASET_OPERATOR: ${SOCIAL_OIDC_GROUPS_DATASET_OPERATOR:-}
  SOCIAL_OIDC_GROUPS_NORMAL: ${SOCIAL_OIDC_GROUPS_NORMAL:-}
  SOCIAL_OIDC_GROUPS_MAPPING: ${SOCIAL_OIDC_GROUPS_MAPPING:-}
  SOCIAL_OIDC_GROUPS_MAPPING_ENABLED: ${SOCIAL_OIDC_GROUPS_MAPPING_ENABLED:-false}
  SOCIAL_OIDC_GROUPS_MAPPING_ADMIN: ${SOCIAL_OIDC_GROUPS_MAPPING_ADMIN:-}
  SOCIAL_OIDC_GROUPS_MAPPING_EDITOR: ${SOCIAL_OIDC_GROUPS_MAPPING_EDITOR:-}
  SOCIAL_OIDC_GROUPS_MAPPING_DATASET_OPERATOR: ${SOCIAL_OIDC_GROUPS_MAPPING_DATASET_OPERATOR:-}
  SOCIAL_OIDC_GROUPS_MAPPING_NORMAL: ${SOCIAL_OIDC_GROUPS_MAPPING_NORMAL:-}
  SOCIAL_OIDC_GROUPS_MAPPING_SEPARATOR: ${SOCIAL_OIDC_GROUPS_MAPPING_SEPARATOR:-,}
  SOCIAL_OIDC_GROUPS_MAPPING_CASE_SENSITIVE: ${SOCIAL_OIDC_GROUPS_MAPPING_CASE_SENSITIVE:-false}
  SOCIAL_OIDC_GROUPS_MAPPING_STRIP_WHITESPACE: ${SOCIAL_OIDC_GROUPS_MAPPING_STRIP_WHITESPACE:-true}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_FLAGS: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_FLAGS:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_CASE_SENSITIVE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_CASE_SENSITIVE:-false}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_STRIP_WHITESPACE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_STRIP_WHITESPACE:-true}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR:-,}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_CASE_SENSITIVE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_CASE_SENSITIVE:-false}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_STRIP_WHITESPACE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_STRIP_WHITESPACE:-true}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_FLAGS: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_FLAGS:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT:-}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT_CASE_SENSITIVE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT_CASE_SENSITIVE:-false}
  SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT_STRIP_WHITESPACE: ${SOCIAL_OIDC_GROUPS_MAPPING_REGEX_REPLACEMENT_SEPARATOR_REGEX_REPLACEMENT_STRIP_WHITESPACE:-true}
  ALLOW_EMBED: ${ALLOW_EMBED:-false}
  QUEUE_MONITOR_THRESHOLD: ${QUEUE_MONITOR_THRESHOLD:-200}
  QUEUE_MONITOR_ALERT_EMAILS: ${QUEUE_MONITOR_ALERT_EMAILS:-}
  QUEUE_MONITOR_INTERVAL: ${QUEUE_MONITOR_INTERVAL:-30}

services:
  # API service - built locally
  api:
    build:
      context: ../api
      dockerfile: Dockerfile
    restart: always
    environment:
      <<: *shared-api-worker-env
      MODE: api
      SENTRY_DSN: ${API_SENTRY_DSN:-}
      SENTRY_TRACES_SAMPLE_RATE: ${API_SENTRY_TRACES_SAMPLE_RATE:-1.0}
      SENTRY_PROFILES_SAMPLE_RATE: ${API_SENTRY_PROFILES_SAMPLE_RATE:-1.0}
    ports:
      - "5001:5001"
    volumes:
      - ./volumes/app/storage:/app/api/storage

  # worker service - built locally
  worker:
    build:
      context: ../api
      dockerfile: Dockerfile
    restart: always
    environment:
      <<: *shared-api-worker-env
      MODE: worker
      SENTRY_DSN: ${API_SENTRY_DSN:-}
      SENTRY_TRACES_SAMPLE_RATE: ${API_SENTRY_TRACES_SAMPLE_RATE:-1.0}
      SENTRY_PROFILES_SAMPLE_RATE: ${API_SENTRY_PROFILES_SAMPLE_RATE:-1.0}
    volumes:
      - ./volumes/app/storage:/app/api/storage

  # Frontend web application - built locally
  web:
    build:
      context: ../web
      dockerfile: Dockerfile
    restart: always
    environment:
      CONSOLE_API_URL: ${CONSOLE_API_URL:-}
      APP_API_URL: ${APP_API_URL:-}
      SENTRY_DSN: ${WEB_SENTRY_DSN:-}
      NEXT_TELEMETRY_DISABLED: ${NEXT_TELEMETRY_DISABLED:-0}
      TEXT_GENERATION_TIMEOUT_MS: ${TEXT_GENERATION_TIMEOUT_MS:-60000}
    ports:
      - "3000:3000"

networks:
  default:
    driver: bridge
