const translation = {
  currentPlan: '當前套餐',
  upgradeBtn: {
    plain: '升級套餐',
    encourage: '立即升級',
    encourageShort: '升級',
  },
  viewBilling: '管理賬單及訂閱',
  buyPermissionDeniedTip: '請聯絡企業管理員訂閱',
  plansCommon: {
    title: '選擇適合您的套餐',
    yearlyTip: '訂閱年度計劃可免費獲得 2 個月！',
    mostPopular: '最受歡迎',
    planRange: {
      monthly: '按月',
      yearly: '按年',
    },
    month: '月',
    year: '年',
    save: '節省',
    currentPlan: '當前計劃',
    contractSales: '聯絡銷售',
    contractOwner: '聯絡團隊管理員',
    free: '免費',
    startForFree: '免費開始',
    getStartedWith: '開始使用',
    contactSales: '聯絡銷售',
    talkToSales: '聯絡銷售',
    modelProviders: '支援的模型提供商',
    teamMembers: '團隊成員',
    buildApps: '構建應用程式數',
    vectorSpace: '向量空間',
    vectorSpaceTooltip: '向量空間是 LLMs 理解您的資料所需的長期記憶系統。',
    vectorSpaceBillingTooltip: '向量儲存是將知識庫向量化處理後為讓 LLMs 理解資料而使用的長期記憶儲存，1MB 大約能滿足 1.2 million character 的向量化後資料儲存（以 OpenAI Embedding 模型估算，不同模型計算方式有差異）。在向量化過程中，實際的壓縮或尺寸減小取決於內容的複雜性和冗餘性。',
    documentsUploadQuota: '文件上傳配額',
    documentProcessingPriority: '文件處理優先順序',
    documentProcessingPriorityTip: '如需更高的文件處理優先順序，請升級您的套餐',
    documentProcessingPriorityUpgrade: '以更快的速度、更高的精度處理更多的資料。',
    priority: {
      'standard': '標準',
      'priority': '優先',
      'top-priority': '最高優先順序',
    },
    logsHistory: '日誌歷史',
    customTools: '自定義工具',
    unavailable: '不可用',
    days: '天',
    unlimited: '無限制',
    support: '支援',
    supportItems: {
      communityForums: '社群論壇',
      emailSupport: '電子郵件支援',
      priorityEmail: '優先電子郵件和聊天支援',
      logoChange: 'Logo 更改',
      SSOAuthentication: 'SSO 認證',
      personalizedSupport: '個性化支援',
      dedicatedAPISupport: '專用 API 支援',
      customIntegration: '自定義整合和支援',
      ragAPIRequest: 'RAG API 請求',
      bulkUpload: '批次上傳文件',
      agentMode: '代理模式',
      workflow: '工作流',
      llmLoadingBalancing: 'LLM 負載均衡',
      llmLoadingBalancingTooltip: '向模型添加多個 API 金鑰，從而有效地繞過 API 速率限制。',
    },
    comingSoon: '即將推出',
    member: '成員',
    memberAfter: '個成員',
    messageRequest: {
      title: '訊息額度',
      tooltip: '為不同方案提供基於 OpenAI 模型的訊息響應額度。',
      titlePerMonth: '{{count,number}} 消息/月',
    },
    annotatedResponse: {
      title: '標註回覆數',
      tooltip: '標註回覆功能透過人工編輯標註為應用提供了可定製的高質量問答回覆能力',
    },
    ragAPIRequestTooltip: '指單獨呼叫 Dify 知識庫資料處理能力的 API。',
    receiptInfo: '只有團隊所有者和團隊管理員才能訂閱和檢視賬單資訊',
    annotationQuota: '註釋配額',
    self: '自我主持',
    apiRateLimitUnit: '{{count,number}}/天',
    freeTrialTipPrefix: '註冊並獲得一個',
    annualBilling: '年度計費',
    freeTrialTipSuffix: '無需信用卡',
    comparePlanAndFeatures: '比較計劃和功能',
    teamMember_one: '{{count,number}} 團隊成員',
    priceTip: '每個工作區/',
    cloud: '雲服務',
    documentsRequestQuota: '{{count,number}}/分鐘 知識請求速率限制',
    unlimitedApiRate: '沒有 API 速率限制',
    apiRateLimitTooltip: 'API 使用次數限制適用於通過 Dify API 所做的所有請求，包括文本生成、聊天對話、工作流執行和文檔處理。',
    getStarted: '開始使用',
    freeTrialTip: '200 次 OpenAI 通話的免費試用。',
    teamWorkspace: '{{count,number}} 團隊工作空間',
    documents: '{{count,number}} 知識文件',
    apiRateLimit: 'API 限速',
    teamMember_other: '{{count,number}} 團隊成員',
    documentsTooltip: '從知識數據來源導入的文件數量配額。',
    documentsRequestQuotaTooltip: '指定工作區在知識基礎中每分鐘可以執行的總操作次數，包括數據集的創建、刪除、更新、文檔上傳、修改、歸檔和知識基礎查詢。這個指標用於評估知識基礎請求的性能。例如，如果一個沙箱用戶在一分鐘內連續執行 10 次命中測試，他們的工作區將在接下來的一分鐘內暫時禁止執行以下操作：數據集的創建、刪除、更新以及文檔上傳或修改。',
  },
  plans: {
    sandbox: {
      name: 'Sandbox',
      description: '200 次 GPT 免費試用',
      includesTitle: '包括：',
      for: '核心功能免費試用',
    },
    professional: {
      name: 'Professional',
      description: '讓個人和小團隊能夠以經濟實惠的方式釋放更多能力。',
      includesTitle: 'Sandbox 計劃中的一切，加上：',
      for: '適合獨立開發者/小型團隊',
    },
    team: {
      name: 'Team',
      description: '協作無限制並享受頂級效能。',
      includesTitle: 'Professional 計劃中的一切，加上：',
      for: '適用於中型團隊',
    },
    enterprise: {
      name: 'Enterprise',
      description: '獲得大規模關鍵任務系統的完整功能和支援。',
      includesTitle: 'Team 計劃中的一切，加上：',
      features: {
        1: '商業許可證授權',
        6: '先進安全與控制',
        3: '多個工作區及企業管理',
        2: '專屬企業功能',
        4: '單一登入',
        8: '專業技術支援',
        0: '企業級可擴展部署解決方案',
        7: 'Dify 官方的更新和維護',
        5: '由 Dify 合作夥伴協商的服務水平協議',
      },
      price: '自訂',
      btnText: '聯繫銷售',
      priceTip: '年度計費のみ',
      for: '適用於大規模團隊',
    },
    community: {
      features: {
        0: '所有核心功能均在公共存儲庫下釋出',
        2: '遵循 Dify 開源許可證',
        1: '單一工作區域',
      },
      includesTitle: '免費功能：',
      btnText: '開始使用社區',
      name: '社區',
      for: '適用於個別用戶、小型團隊或非商業項目',
      description: '適用於個別用戶、小型團隊或非商業項目',
      price: '免費',
    },
    premium: {
      features: {
        2: '網頁應用程序標誌及品牌自定義',
        0: '各種雲端服務提供商的自我管理可靠性',
        1: '單一工作區域',
        3: '優先電子郵件及聊天支持',
      },
      for: '適用於中型組織和團隊',
      comingSoon: '微軟 Azure 與 Google Cloud 支持即將推出',
      priceTip: '根據雲端市場',
      btnText: '獲取高級版在',
      name: '高級',
      description: '適用於中型組織和團隊',
      price: '可擴展的',
      includesTitle: '來自社群的一切，加上：',
    },
  },
  vectorSpace: {
    fullTip: '向量空間已滿。',
    fullSolution: '升級您的套餐以獲得更多空間。',
  },
  apps: {
    fullTipLine1: '升級您的套餐以',
    fullTipLine2: '構建更多的程式。',
    fullTip1: '升級以創建更多應用程序',
    fullTip2des: '建議清除不活躍的應用程式以釋放使用空間，或聯繫我們。',
    contactUs: '聯繫我們',
    fullTip1des: '您已達到此計劃建構應用程序的限制',
    fullTip2: '計劃限制已達',
  },
  annotatedResponse: {
    fullTipLine1: '升級您的套餐以',
    fullTipLine2: '標註更多對話。',
    quotaTitle: '標註的配額',
  },
  usagePage: {
    documentsUploadQuota: '文件上傳配額',
    vectorSpaceTooltip: '使用高品質索引模式的文件將消耗知識數據存儲資源。當知識數據存儲達到限制後，將不會上傳新文件。',
    annotationQuota: '註解配額',
    vectorSpace: '知識數據儲存',
    buildApps: '建構應用程式',
    teamMembers: '團隊成員',
  },
  teamMembers: '團隊成員',
}

export default translation
