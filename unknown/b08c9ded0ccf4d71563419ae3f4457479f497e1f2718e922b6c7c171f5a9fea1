const translation = {
  title: '검색 테스트',
  desc: '주어진 쿼리 텍스트에 기반하여 지식의 검색 효과를 테스트합니다.',
  dateTimeFormat: 'YYYY/MM/DD HH:mm',
  recents: '최근 결과',
  table: {
    header: {
      source: '소스',
      text: '텍스트',
      time: '시간',
    },
  },
  input: {
    title: '소스 텍스트',
    placeholder: '텍스트를 입력하세요. 간결한 설명문이 좋습니다.',
    countWarning: '최대 200 자까지 입력할 수 있습니다.',
    indexWarning: '고품질 지식만.',
    testing: '테스트 중',
  },
  hit: {
    title: '검색 결과 단락',
    emptyTip: '검색 테스트 결과가 여기에 표시됩니다.',
  },
  noRecentTip: '최근 쿼리 결과가 없습니다.',
  viewChart: '벡터 차트 보기',
  settingTitle: '검색 설정',
  viewDetail: '자세히보기',
  open: '열다',
  records: '레코드',
  hitChunks: '{{num}}개의 자식 청크를 히트했습니다.',
  keyword: '키워드',
  chunkDetail: '청크 디테일 (Chunk Detail)',
}

export default translation
