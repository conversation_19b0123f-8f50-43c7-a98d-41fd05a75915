const translation = {
  metadata: {
    title: 'Vtičniki',
  },
  category: {
    bundles: 'Paketi',
    all: 'Vse',
    extensions: 'Raz<PERSON><PERSON>tve',
    models: 'Modeli',
    agents: 'Strategije agenta',
    tools: 'Orodja',
  },
  categorySingle: {
    extension: '<PERSON><PERSON><PERSON><PERSON>te<PERSON>',
    bundle: 'Pak<PERSON>',
    agent: 'Agentska strategija',
    tool: 'Orodje',
    model: 'Model',
  },
  list: {
    source: {
      local: 'Namestite iz lokalne paketne datoteke',
      marketplace: 'Namestite iz tržnice',
      github: 'Namestite iz GitHub-a',
    },
    notFound: 'Nobeni vtičniki niso bili najdeni.',
    noInstalled: 'Nobeni vtičniki niso nameščeni.',
  },
  source: {
    marketplace: 'Tržnica',
    github: 'GitHub',
    local: 'Lokalna paketna datoteka',
  },
  detailPanel: {
    categoryTip: {
      local: 'Lokalni vtičnik',
      marketplace: 'Nameščeno iz tržnice',
      debugging: 'Orodje za odpravljanje napak',
      github: 'Name<PERSON><PERSON><PERSON> iz Githuba',
    },
    operation: {
      remove: 'Ods<PERSON>ni',
      install: 'Namestite',
      viewDetail: 'Oglej si podrobnosti',
      detail: 'Podrobnosti',
      update: 'Posodobitev',
      checkUpdate: 'Preveri posodobitev',
      info: 'Informacije o vtičniku',
    },
    toolSelector: {
      unsupportedContent: 'V različici vtičnika, ki je nameščena, ta akcija ni zagotovljena.',
      unsupportedContent2: 'Kliknite za preklop različice.',
      params: 'RAZLOGOVANJE KONFIGURACIJA',
      auto: 'Samodejno',
      title: 'Dodaj orodje',
      settings: 'UPORABNIŠKE NASTAVITVE',
      descriptionLabel: 'Opis orodja',
      uninstalledLink: 'Upravljanje v vtičnikih',
      unsupportedTitle: 'Nepodprta akcija',
      placeholder: 'Izberite orodje...',
      uninstalledTitle: 'Orodje ni nameščeno',
      uninstalledContent: 'Ta vtičnik je nameščen iz lokalnega/GitHub repozitorija. Uporabite ga prosim po namestitvi.',
      toolLabel: 'Orodje',
      descriptionPlaceholder: 'Kratek opis namena orodja, npr. pridobitev temperature za določeno lokacijo.',
      empty: 'Kliknite gumb \' \' za dodajanje orodij. Dodate lahko več orodij.',
      paramsTip1: 'Nadzoruje parametre sklepanja LLM.',
      paramsTip2: 'Ko je \'Avtomatsko\' izklopljeno, se uporablja privzeta vrednost.',
      toolSetting: 'Nastavitve orodja',
    },
    endpointDisableContent: 'Ali želite onemogočiti {{name}}?',
    serviceOk: 'Storitve so v redu',
    endpointDeleteTip: 'Odstrani končno točko',
    actionNum: '{{num}} {{action}} VKLJUČENO',
    endpointDeleteContent: 'Ali želite odstraniti {{name}}?',
    configureApp: 'Konfiguriraj aplikacijo',
    endpointsDocLink: 'Oglejte si dokument',
    endpointModalTitle: 'Nastavi končno točko',
    disabled: 'Onemogočeno',
    configureTool: 'Konfigurirajte orodje',
    switchVersion: 'Preklopna različica',
    strategyNum: '{{num}} {{strategy}} VKLJUČENO',
    endpoints: 'Končne točke',
    configureModel: 'Konfiguriraj model',
    modelNum: '{{num}} VZORCI VKLJUČENI',
    endpointDisableTip: 'Onemogoči končno točko',
    endpointsTip: 'Ta vtičnik zagotavlja specifične funkcionalnosti preko končnih točk, prav tako pa lahko konfigurirate več nizov končnih točk za trenutno delovno okolje.',
    endpointModalDesc: 'Ko je konfiguriran, se lahko uporabljajo funkcije, ki jih vtičnik zagotavlja prek API končnih točk.',
    endpointsEmpty: 'Kliknite gumb \' \' za dodajanje končne točke',
  },
  debugInfo: {
    viewDocs: 'Oglejte si dokumente',
    title: 'Odpravljanje napak',
  },
  privilege: {
    whoCanInstall: 'Kdo lahko namesti in upravlja vtičnike?',
    title: 'Nastavitve vtičnika',
    admins: 'Administratori',
    whoCanDebug: 'Kdo lahko odpravi napake v vtičnikih?',
    everyone: 'Vsi',
    noone: 'Nihče',
  },
  pluginInfoModal: {
    title: 'Informacije o vtičniku',
    packageName: 'Paket',
    release: 'Izdati',
    repository: 'Shramba',
  },
  action: {
    usedInApps: 'Ta vtičnik se uporablja v {{num}} aplikacijah.',
    checkForUpdates: 'Preverite posodobitve',
    deleteContentLeft: 'Ali želite odstraniti',
    deleteContentRight: 'vtičnik?',
    delete: 'Odstrani vtičnik',
    pluginInfo: 'Informacije o vtičniku',
  },
  installModal: {
    labels: {
      repository: 'Shramba',
      version: 'Različica',
      package: 'Paket',
    },
    installFailed: 'Namestitev ni uspela',
    installing: 'Nameščanje...',
    installedSuccessfully: 'Namestitev uspešna',
    uploadFailed: 'Nalaganje ni uspelo',
    pluginLoadErrorDesc: 'Ta vtičnik ne bo nameščen',
    readyToInstallPackages: 'Prihajamo do namestitve naslednjih {{num}} dodatkov',
    cancel: 'Prekliči',
    fromTrustSource: 'Prosimo, poskrbite, da namestite le vtičnike iz <trustSource>zaupanja vrednega vira</trustSource>.',
    installedSuccessfullyDesc: 'Vtičnik je bil uspešno nameščen.',
    readyToInstallPackage: 'Namestitev naslednjega vtičnika',
    installComplete: 'Namestitev končana',
    installFailedDesc: 'Namestitev vtičnika je bila neuspešna.',
    close: 'Zapri',
    uploadingPackage: 'Nalagam {{packageName}}...',
    readyToInstall: 'Namestitev naslednjega vtičnika',
    dropPluginToInstall: 'Tukaj spustite paket vtičnika, da ga namestite',
    next: 'Naprej',
    back: 'Nazaj',
    install: 'Namestite',
    pluginLoadError: 'Napaka pri nalaganju vtičnika',
    installPlugin: 'Namestite vtičnik',
    installWarning: 'Ta vtičnik ni dovoljen za namestitev.',
  },
  installFromGitHub: {
    updatePlugin: 'Posodobite vtičnik iz GitHuba',
    gitHubRepo: 'GitHub repozitorij',
    installFailed: 'Namestitev ni uspela',
    installPlugin: 'Namestite vtičnik iz GitHuba',
    selectVersionPlaceholder: 'Prosim, izberite različico',
    selectPackagePlaceholder: 'Prosim, izberite paket',
    selectPackage: 'Izberite paket',
    uploadFailed: 'Nalaganje ni uspelo',
    selectVersion: 'Izberite različico',
    installedSuccessfully: 'Namestitev uspešna',
    installNote: 'Prosim, prepričajte se, da namestite vtičnike samo iz zaupanja vrednega vira.',
  },
  upgrade: {
    close: 'Zapri',
    description: 'Namestitev naslednjega vtičnika',
    upgrading: 'Nameščanje...',
    successfulTitle: 'Namestitev uspešna',
    upgrade: 'Namestite',
    usedInApps: 'Uporablja se v {{num}} aplikacijah',
    title: 'Namestite vtičnik',
  },
  error: {
    noReleasesFound: 'Ni najdenih izdaj. Prosimo preverite GitHub repozitorij ali vhodni URL.',
    fetchReleasesError: 'Ne morem pridobiti izdaj. Prosim, poskusite znova pozneje.',
    inValidGitHubUrl: 'Neveljavna GitHub povezava. Vnesite veljavno povezavo v formatu: https://github.com/lastnik/repo',
  },
  marketplace: {
    sortOption: {
      mostPopular: 'Najbolj priljubljeno',
      firstReleased: 'Prvič izdan',
      recentlyUpdated: 'Nedavno posodobljeno',
      newlyReleased: 'Nedavno izdano',
    },
    and: 'in',
    pluginsResult: '{{num}} rezultati',
    sortBy: 'Razvrsti po',
    verifiedTip: 'Verificirano s strani Dify',
    discover: 'Odkrijte',
    partnerTip: 'Potrjeno s strani partnerja Dify',
    empower: 'Okrepite svoj razvoj AI',
    noPluginFound: 'Nobenega vtičnika ni bilo najti.',
    viewMore: 'Oglejte si več',
    moreFrom: 'Več iz tržnice',
    difyMarketplace: 'Dify Marketplace',
  },
  task: {
    installing: 'Namestitev {{installingLength}} vtičnikov, 0 končanih.',
    clearAll: 'Počisti vse',
    installError: '{{errorLength}} vtičnikov ni uspelo namestiti, kliknite za ogled',
    installingWithSuccess: 'Namestitev {{installingLength}} dodatkov, {{successLength}} uspešnih.',
    installedError: '{{errorLength}} vtičnikov ni uspelo namestiti',
    installingWithError: 'Namestitev {{installingLength}} vtičnikov, {{successLength}} uspešnih, {{errorLength}} neuspešnih',
  },
  endpointsEnabled: '{{num}} nizov končnih točk omogočenih',
  search: 'Iskanje',
  searchInMarketplace: 'Iskanje na trgu',
  searchPlugins: 'Išči vtičnike',
  fromMarketplace: 'Iz tržnice',
  searchTools: 'Iskalna orodja...',
  installPlugin: 'Namestite vtičnik',
  from: 'Iz',
  installFrom: 'NAMESTITE IZ',
  searchCategories: 'Išči kategorije',
  installAction: 'Namestite',
  findMoreInMarketplace: 'Poiščite več v Tržnici',
  install: '{{num}} namestitev',
  allCategories: 'Vse kategorije',
  difyVersionNotCompatible: 'Trenutna različica Dify ni združljiva s to vtičnico, prosimo, posodobite na minimalno zahtevano različico: {{minimalDifyVersion}}',
  requestAPlugin: 'Zahtevajte vtičnik',
  publishPlugins: 'Objavljanje vtičnikov',
}

export default translation
