const translation = {
  pageTitle: '시작하기 🎉',
  welcome: 'Dify 에 오신 것을 환영합니다. 계속하려면 로그인하세요.',
  email: '이메일 주소',
  emailPlaceholder: '이메일 주소를 입력하세요',
  password: '비밀번호',
  passwordPlaceholder: '비밀번호를 입력하세요',
  name: '사용자 이름',
  namePlaceholder: '사용자 이름을 입력하세요',
  forget: '비밀번호를 잊으셨나요?',
  signBtn: '로그인',
  installBtn: '설치',
  setAdminAccount: '관리자 계정 설정',
  setAdminAccountDesc: '앱 생성 및 LLM 제공자 관리 등 최고 권한을 가진 관리자 계정 설정',
  createAndSignIn: '계정 생성 및 로그인',
  oneMoreStep: '마지막 단계',
  createSample: '이 정보를 기반으로 샘플 앱을 생성합니다.',
  invitationCode: '초대 코드',
  invitationCodePlaceholder: '초대 코드를 입력하세요',
  interfaceLanguage: '인터페이스 언어',
  timezone: '시간대',
  go: 'Dify 로 이동',
  sendUsMail: '간단한 소개를 메일로 보내주시면 초대 요청을 처리해드립니다.',
  acceptPP: '개인정보 처리 방침에 동의합니다.',
  reset: '비밀번호를 재설정하려면 다음 명령을 실행하세요:',
  withGitHub: 'GitHub 로 계속',
  withGoogle: 'Google 로 계속',
  rightTitle: 'LLM 의 최대 잠재력을 발휘하세요',
  rightDesc: '매력적이고 조작 가능하며 개선 가능한 AI 애플리케이션을 쉽게 구축하세요.',
  tos: '이용약관',
  pp: '개인정보 처리 방침',
  tosDesc: '가입함으로써 다음 내용에 동의하게 됩니다.',
  goToInit: '계정이 초기화되지 않았다면 초기화 페이지로 이동하세요.',
  dontHave: '계정이 없으신가요?',
  invalidInvitationCode: '유효하지 않은 초대 코드입니다.',
  accountAlreadyInited: '계정은 이미 초기화되었습니다.',
  forgotPassword: '비밀번호를 잊으셨나요?',
  resetLinkSent: '재설정 링크가 전송되었습니다',
  sendResetLink: '재설정 링크 보내기',
  backToSignIn: '로그인으로 돌아가기',
  forgotPasswordDesc: '비밀번호를 재설정하려면 이메일 주소를 입력하세요. 비밀번호 재설정 방법에 대한 이메일을 보내드리겠습니다.',
  checkEmailForResetLink: '비밀번호 재설정 링크를 확인하려면 이메일을 확인하세요. 몇 분 내에 나타나지 않으면 스팸 폴더를 확인하세요.',
  passwordChanged: '지금 로그인',
  changePassword: '비밀번호 변경',
  changePasswordTip: '계정의 새 비밀번호를 입력하세요',
  invalidToken: '유효하지 않거나 만료된 토큰',
  confirmPassword: '비밀번호 확인',
  confirmPasswordPlaceholder: '새 비밀번호를 확인하세요',
  passwordChangedTip: '비밀번호가 성공적으로 변경되었습니다',
  error: {
    emailEmpty: '이메일 주소를 입력하세요.',
    emailInValid: '유효한 이메일 주소를 입력하세요.',
    nameEmpty: '사용자 이름을 입력하세요.',
    passwordEmpty: '비밀번호를 입력하세요.',
    passwordInvalid: '비밀번호는 문자와 숫자를 포함하고 8 자 이상이어야 합니다.',
    passwordLengthInValid: '비밀번호는 8 자 이상이어야 합니다.',
    registrationNotAllowed: '계정을 찾을 수 없습니다. 등록하려면 시스템 관리자에게 문의하십시오.',
  },
  license: {
    tip: 'Dify Community Edition 을 시작하기 전에 GitHub 의',
    link: '오픈 소스 라이선스',
  },
  join: '가입하기',
  joinTipStart: '당신을 초대합니다.',
  joinTipEnd: '팀에 가입하세요.',
  invalid: '링크의 유효 기간이 만료되었습니다.',
  explore: 'Dify 를 탐색하세요',
  activatedTipStart: '이제',
  activatedTipEnd: '팀에 가입되었습니다.',
  activated: '지금 로그인하세요',
  adminInitPassword: '관리자 초기화 비밀번호',
  validate: '확인',
  sso: 'SSO 로 계속하기',
  checkCode: {
    verify: '확인',
    verificationCode: '인증 코드',
    tips: '<strong>{{email}}</strong>로 인증 코드를 보내드립니다.',
    validTime: '코드는 5 분 동안 유효합니다',
    checkYourEmail: '이메일 주소 확인',
    invalidCode: '유효하지 않은 코드',
    verificationCodePlaceholder: '6 자리 코드 입력',
    emptyCode: '코드가 필요합니다.',
    useAnotherMethod: '다른 방법 사용',
    didNotReceiveCode: '코드를 받지 못하셨나요?',
    resend: '재전송',
  },
  back: '뒤로',
  or: '또는',
  useVerificationCode: '인증 코드 사용',
  continueWithCode: '코드로 계속하기',
  usePassword: '비밀번호 사용',
  withSSO: 'SSO 로 계속하기',
  backToLogin: '로그인으로 돌아가기',
  resetPassword: '비밀번호 재설정',
  setYourAccount: '계정 설정',
  noLoginMethod: '인증 방법이 구성되지 않음',
  sendVerificationCode: '인증 코드 보내기',
  changePasswordBtn: '비밀번호 설정',
  enterYourName: '사용자 이름을 입력해 주세요',
  noLoginMethodTip: '인증 방법을 추가하려면 시스템 관리자에게 문의하십시오.',
  resetPasswordDesc: 'Dify 에 가입할 때 사용한 이메일을 입력하면 비밀번호 재설정 이메일을 보내드립니다.',
  licenseInactiveTip: '작업 영역에 대한 Dify Enterprise 라이선스가 비활성 상태입니다. Dify 를 계속 사용하려면 관리자에게 문의하십시오.',
  licenseLost: '라이센스 분실',
  licenseLostTip: 'Dify 라이선스 서버에 연결하지 못했습니다. Dify 를 계속 사용하려면 관리자에게 문의하십시오.',
  licenseInactive: 'License Inactive(라이선스 비활성)',
  licenseExpired: '라이센스가 만료되었습니다.',
  licenseExpiredTip: '작업 영역에 대한 Dify Enterprise 라이선스가 만료되었습니다. Dify 를 계속 사용하려면 관리자에게 문의하십시오.',
  webapp: {
    noLoginMethod: '웹 애플리케이션에 대한 인증 방법이 구성되어 있지 않습니다.',
    disabled: '웹앱 인증이 비활성화되었습니다. 이를 활성화하려면 시스템 관리자에게 문의하십시오. 앱을 직접 사용해 볼 수 있습니다.',
    noLoginMethodTip: '인증 방법을 추가하려면 시스템 관리자에게 연락하십시오.',
  },
}

export default translation
