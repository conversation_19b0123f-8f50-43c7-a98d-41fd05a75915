const translation = {
  steps: {
    header: {
      creation: 'สร้างความรู้',
      update: 'เพิ่มข้อมูล',
      fallbackRoute: 'ความรู้',
    },
    one: 'เลือกแหล่งข้อมูล',
    two: 'การประมวลผลและการทําความสะอาดข้อความล่วงหน้า',
    three: 'ดําเนินการและเสร็จสิ้น',
  },
  error: {
    unavailable: 'ความรู้นี้ไม่มี',
  },
  firecrawl: {
    configFirecrawl: 'กําหนดค่า 🔥Firecrawl',
    apiKeyPlaceholder: 'คีย์ API จาก firecrawl.dev',
    getApiKeyLinkText: 'รับคีย์ API ของคุณจาก firecrawl.dev',
  },
  jinaReader: {
    configJinaReader: 'กําหนดค่า Jina Reader',
    apiKeyPlaceholder: 'คีย์ API จาก jina.ai',
    getApiKeyLinkText: 'รับคีย์ API ฟรีได้ที่ jina.ai',
  },
  stepOne: {
    filePreview: 'ตัวอย่างไฟล์',
    pagePreview: 'ตัวอย่างหน้า',
    dataSourceType: {
      file: 'นําเข้าจากไฟล์',
      notion: 'ซิงค์จาก Notion',
      web: 'ซิงค์จากเว็บไซต์',
    },
    uploader: {
      title: 'อัปโหลดไฟล์',
      button: 'ลากและวางไฟล์หรือโฟลเดอร์หรือ',
      browse: 'เล็ม',
      tip: 'รองรับ {{supportTypes}} สูงสุด {{size}}MB แต่ละตัว',
      validation: {
        typeError: 'ไม่รองรับประเภทไฟล์',
        size: 'ไฟล์ใหญ่เกินไป สูงสุดคือ {{size}}MB',
        count: 'ไม่รองรับหลายไฟล์',
        filesNumber: 'คุณถึงขีดจํากัดการอัปโหลดเป็นชุดของ {{filesNumber}} แล้ว',
      },
      cancel: 'ยกเลิก',
      change: 'เปลี่ยน',
      failed: 'อัปโหลดล้มเหลว',
    },
    notionSyncTitle: 'ความคิดไม่เชื่อมต่อ',
    notionSyncTip: 'ในการซิงค์กับ Notion ต้องสร้างการเชื่อมต่อกับ Notion ก่อน',
    connect: 'ไปที่เชื่อมต่อ',
    button: 'ต่อไป',
    emptyDatasetCreation: 'ฉันต้องการสร้างความรู้ที่ว่างเปล่า',
    modal: {
      title: 'สร้างความรู้ที่ว่างเปล่า',
      tip: 'ความรู้ที่ว่างเปล่าจะไม่มีเอกสาร และคุณสามารถอัปโหลดเอกสารได้ตลอดเวลา',
      input: 'ชื่อความรู้',
      placeholder: 'กรุณาป้อน',
      nameNotEmpty: 'ชื่อต้องไม่ว่างเปล่า',
      nameLengthInvalid: 'ชื่อต้องมีอักขระระหว่าง 1 ถึง 40 ตัว',
      cancelButton: 'ยกเลิก',
      confirmButton: 'สร้าง',
      failed: 'การสร้างล้มเหลว',
    },
    website: {
      chooseProvider: 'เลือกผู้ให้บริการ',
      fireCrawlNotConfigured: 'ไม่ได้กําหนดค่า Firecrawl',
      fireCrawlNotConfiguredDescription: 'กําหนดค่า Firecrawl ด้วยคีย์ API เพื่อใช้งาน',
      jinaReaderNotConfigured: 'ไม่ได้กําหนดค่า Jina Reader',
      jinaReaderNotConfiguredDescription: 'ตั้งค่า Jina Reader โดยป้อนคีย์ API ฟรีเพื่อเข้าถึง',
      configure: 'กําหนดค่า',
      run: 'วิ่ง',
      firecrawlTitle: 'แยกเนื้อหาเว็บด้วย 🔥Firecrawl',
      firecrawlDoc: 'เอกสาร Firecrawl',
      jinaReaderTitle: 'แปลงทั้งไซต์เป็น Markdown',
      jinaReaderDoc: 'เรียนรู้เพิ่มเติมเกี่ยวกับ Jina Reader',
      jinaReaderDocLink: 'https://jina.ai/reader',
      useSitemap: 'ใช้แผนผังเว็บไซต์',
      useSitemapTooltip: 'ทําตามแผนผังเว็บไซต์เพื่อรวบรวมข้อมูลเว็บไซต์ หากไม่เป็นเช่นนั้น Jina Reader จะรวบรวมข้อมูลซ้ําๆ ตามความเกี่ยวข้องของหน้า โดยให้หน้าเว็บน้อยลงแต่มีคุณภาพสูงกว่า',
      options: 'ตัวเลือก',
      crawlSubPage: 'รวบรวมข้อมูลหน้าย่อย',
      limit: 'เขต',
      maxDepth: 'ความลึกสูงสุด',
      excludePaths: 'ยกเว้นเส้นทาง',
      includeOnlyPaths: 'รวมเฉพาะเส้นทาง',
      extractOnlyMainContent: 'แยกเฉพาะเนื้อหาหลัก (ไม่มีส่วนหัว การนําทาง ส่วนท้าย ฯลฯ)',
      exceptionErrorTitle: 'มีข้อยกเว้นเกิดขึ้นขณะรันงานการรวบรวมข้อมูล:',
      unknownError: 'ข้อผิดพลาดที่ไม่รู้จัก',
      totalPageScraped: 'จํานวนหน้าที่ขูด:',
      selectAll: 'เลือกทั้งหมด',
      resetAll: 'รีเซ็ตทั้งหมด',
      scrapTimeInfo: 'ขูด {{total}} หน้าทั้งหมดภายใน {{time}}s',
      preview: 'ดูตัวอย่าง',
      maxDepthTooltip: 'ความลึกสูงสุดในการรวบรวมข้อมูลเมื่อเทียบกับ URL ที่ป้อน ความลึก 0 เพียงแค่ขูดหน้าของ URL ที่ป้อนความลึก 1 ขูด url และทุกอย่างหลังจาก enteredURL + หนึ่ง / เป็นต้น',
      watercrawlTitle: 'ดึงเนื้อหาจากเว็บด้วย Watercrawl',
      configureJinaReader: 'ตั้งค่า Jina Reader',
      configureFirecrawl: 'กำหนดค่า Firecrawl',
      configureWatercrawl: 'กำหนดค่าการเข้าถึงน้ำ',
      waterCrawlNotConfiguredDescription: 'กำหนดค่า Watercrawl ด้วย API key เพื่อใช้งาน.',
      watercrawlDoc: 'เอกสาร Watercrawl',
      waterCrawlNotConfigured: 'Watercrawl ยังไม่ได้ตั้งค่า',
    },
    cancel: 'ยกเลิก',
  },
  stepTwo: {
    segmentation: 'การตั้งค่าก้อน',
    auto: 'อัตโนมัติ',
    autoDescription: 'ตั้งค่ากฎการแบ่งกลุ่มและการประมวลผลล่วงหน้าโดยอัตโนมัติ ขอแนะนําให้ผู้ใช้ที่ไม่คุ้นเคยเลือกสิ่งนี้',
    custom: 'ธรรมเนียม',
    customDescription: 'ปรับแต่งกฎของกลุ่ม ความยาวของกลุ่ม และกฎการประมวลผลล่วงหน้า ฯลฯ',
    separator: 'ตัวคั่น',
    separatorTip: 'ตัวคั่นคืออักขระที่ใช้ในการแยกข้อความ \\n\\n และ \\n เป็นตัวคั่นที่ใช้กันทั่วไปสําหรับการแยกย่อหน้าและบรรทัด เมื่อรวมกับเครื่องหมายจุลภาค (\\n\\n,\\n) ย่อหน้าจะถูกแบ่งตามบรรทัดเมื่อเกินความยาวของก้อนสูงสุด คุณยังสามารถใช้ตัวคั่นพิเศษที่กําหนดโดยตัวคุณเอง (เช่น ***)',
    separatorPlaceholder: '\\n\\n สําหรับแยกย่อหน้า \\n สําหรับแยกเส้น',
    maxLength: 'ความยาวก้อนสูงสุด',
    maxLengthCheck: 'ความยาวก้อนสูงสุดควรน้อยกว่า {{limit}}',
    overlap: 'การทับซ้อนกันของก้อน',
    overlapTip: 'การตั้งค่าการทับซ้อนกันของกลุ่มสามารถรักษาความเกี่ยวข้องทางความหมายระหว่างกันได้ ขอแนะนําให้ตั้งค่า 10%-25% ของขนาดก้อนสูงสุด',
    overlapCheck: 'การทับซ้อนกันของก้อนไม่ควรใหญ่กว่าความยาวของก้อนสูงสุด',
    rules: 'กฎการประมวลผลข้อความล่วงหน้า',
    removeExtraSpaces: 'แทนที่ช่องว่างบรรทัดใหม่และแท็บที่ต่อเนื่องกัน',
    removeUrlEmails: 'ลบ URL และที่อยู่อีเมลทั้งหมด',
    removeStopwords: 'ลบคําหยุด เช่น "a", "an", "the"',
    preview: 'ยืนยันและดูตัวอย่าง',
    reset: 'รี เซ็ต',
    indexMode: 'โหมดดัชนี',
    qualified: 'คุณภาพสูง',
    recommend: 'แนะนำ',
    qualifiedTip: 'เรียกใช้อินเทอร์เฟซการฝังระบบเริ่มต้นสําหรับการประมวลผลเพื่อให้มีความแม่นยําสูงขึ้นเมื่อผู้ใช้สืบค้น',
    warning: 'โปรดตั้งค่าคีย์ API ของผู้ให้บริการโมเดลก่อน',
    click: 'ไปที่การตั้งค่า',
    economical: 'ประหยัด',
    economicalTip: 'ใช้เอ็นจิ้นเวกเตอร์ออฟไลน์ ดัชนีคําหลัก ฯลฯ เพื่อลดความแม่นยําโดยไม่ต้องใช้โทเค็น',
    QATitle: 'การแบ่งกลุ่มในรูปแบบคําถามและคําตอบ',
    QATip: 'การเปิดใช้งานตัวเลือกนี้จะใช้โทเค็นมากขึ้น',
    QALanguage: 'แบ่งกลุ่มโดยใช้',
    estimateCost: 'กะ',
    estimateSegment: 'ก้อนโดยประมาณ',
    segmentCount: 'ก้อน',
    calculating: 'คำนวณ   ',
    fileSource: 'เตรียมเอกสารล่วงหน้า',
    notionSource: 'หน้าประมวลผลล่วงหน้า',
    websiteSource: 'เว็บไซต์ Preprocess',
    other: 'และอื่น ๆ',
    fileUnit: 'แฟ้ม',
    notionUnit: 'หน้า',
    webpageUnit: 'หน้า',
    previousStep: 'ขั้นตอนก่อนหน้า',
    nextStep: 'บันทึกและประมวลผล',
    save: 'บันทึกและประมวลผล',
    cancel: 'ยกเลิก',
    sideTipTitle: 'ทําไมต้องแบ่งกลุ่มและเตรียมกระบวนการล่วงหน้า?',
    sideTipP1: 'เมื่อประมวลผลข้อมูลข้อความ การแบ่งกลุ่มและการทําความสะอาดเป็นขั้นตอนการประมวลผลล่วงหน้าที่สําคัญสองขั้นตอน',
    sideTipP2: 'การแบ่งส่วนจะแบ่งข้อความยาวออกเป็นย่อหน้าเพื่อให้โมเดลเข้าใจได้ดีขึ้น สิ่งนี้ช่วยปรับปรุงคุณภาพและความเกี่ยวข้องของผลลัพธ์ของแบบจําลอง',
    sideTipP3: 'การทําความสะอาดจะลบอักขระและรูปแบบที่ไม่จําเป็น ทําให้ความรู้สะอาดขึ้นและง่ายต่อการแยกวิเคราะห์',
    sideTipP4: 'การแบ่งส่วนและการทําความสะอาดที่เหมาะสมช่วยปรับปรุงประสิทธิภาพของโมเดล ให้ผลลัพธ์ที่แม่นยําและมีคุณค่ามากขึ้น',
    previewTitle: 'ดูตัวอย่าง',
    previewTitleButton: 'ดูตัวอย่าง',
    previewButton: 'การเปลี่ยนไปใช้รูปแบบ Q&A',
    previewSwitchTipStart: 'การแสดงตัวอย่างส่วนปัจจุบันอยู่ในรูปแบบข้อความ การเปลี่ยนไปใช้ตัวอย่างรูปแบบคําถามและคําตอบจะ',
    previewSwitchTipEnd: 'ใช้โทเค็นเพิ่มเติม',
    characters: 'อักขระ',
    indexSettingTip: 'หากต้องการเปลี่ยนวิธีการจัดทําดัชนีและรูปแบบการฝัง โปรดไปที่',
    retrievalSettingTip: 'หากต้องการเปลี่ยนการตั้งค่าการดึงข้อมูล โปรดไปที่',
    datasetSettingLink: 'การตั้งค่าความรู้',
    notAvailableForParentChild: 'ไม่สามารถใช้ได้กับ ดัชนีผู้ปกครอง-ลูก',
    qaSwitchHighQualityTipContent: 'ปัจจุบัน มีเพียงวิธีการจัดทําดัชนีคุณภาพสูงเท่านั้นที่รองรับการแบ่งกลุ่มรูปแบบ Q&A คุณต้องการเปลี่ยนไปใช้โหมดคุณภาพสูงหรือไม่?',
    fullDoc: 'เอกสารฉบับเต็ม',
    parentChild: 'พ่อแม่ลูก',
    parentChunkForContext: 'Parent-chunk สําหรับบริบท',
    general: 'ทั่วไป',
    parentChildChunkDelimiterTip: 'ตัวคั่นคืออักขระที่ใช้ในการแยกข้อความ \\n แนะนําให้ใช้สําหรับการแยกก้อนหลักออกเป็นก้อนย่อยขนาดเล็ก คุณยังสามารถใช้ตัวคั่นพิเศษที่กําหนดโดยตัวคุณเอง',
    previewChunkCount: '{{นับ}} ก้อนโดยประมาณ',
    fullDocTip: 'เอกสารทั้งหมดจะถูกใช้เป็นส่วนหลักและดึงข้อมูลโดยตรง โปรดทราบว่าด้วยเหตุผลด้านประสิทธิภาพ ข้อความที่เกิน 10,000 โทเค็นจะถูกตัดทอนโดยอัตโนมัติ',
    useQALanguage: 'ก้อนโดยใช้รูปแบบ Q&A ใน',
    switch: 'เปลี่ยน',
    paragraphTip: 'โหมดนี้จะแบ่งข้อความออกเป็นย่อหน้าตามตัวคั่นและความยาวของกลุ่มสูงสุด โดยใช้ข้อความที่แยกเป็นส่วนหลักสําหรับการดึงข้อมูล',
    childChunkForRetrieval: 'ก้อนเด็กสําหรับการดึงข้อมูล',
    parentChildDelimiterTip: 'ตัวคั่นคืออักขระที่ใช้ในการแยกข้อความ \\n\\n แนะนําให้ใช้สําหรับการแบ่งเอกสารต้นฉบับออกเป็นส่วนหลักขนาดใหญ่ คุณยังสามารถใช้ตัวคั่นพิเศษที่กําหนดโดยตัวคุณเอง',
    qaSwitchHighQualityTipTitle: 'รูปแบบ Q&A ต้องใช้วิธีการจัดทําดัชนีคุณภาพสูง',
    highQualityTip: 'เมื่อฝังในโหมดคุณภาพสูงเสร็จแล้ว จะไม่สามารถเปลี่ยนกลับเป็นโหมดประหยัดได้',
    generalTip: 'โหมดการแบ่งกลุ่มข้อความทั่วไป กลุ่มที่ดึงและเรียกคืนจะเหมือนกัน',
    previewChunkTip: 'คลิกปุ่ม \'Preview Chunk\' ทางด้านซ้ายเพื่อโหลดตัวอย่าง',
    previewChunk: 'ดูตัวอย่าง Chunk',
    notAvailableForQA: 'ไม่สามารถใช้ได้กับ Q&A Index',
    paragraph: 'วรรค',
    parentChildTip: 'เมื่อใช้โหมดผู้ปกครอง-รอง child-chunk จะใช้สําหรับการดึงข้อมูล และ parent-chunk จะใช้สําหรับการเรียกคืนเป็นบริบท',
  },
  stepThree: {
    creationTitle: '🎉 สร้างความรู้',
    creationContent: 'เราตั้งชื่อความรู้โดยอัตโนมัติ คุณสามารถแก้ไขได้ตลอดเวลา',
    label: 'ชื่อความรู้',
    additionTitle: '🎉 อัปโหลดเอกสาร',
    additionP1: 'เอกสารถูกอัปโหลดไปยังความรู้แล้ว',
    additionP2: 'คุณสามารถค้นหาได้ในรายการเอกสารของความรู้',
    stop: 'หยุดการประมวลผล',
    resume: 'ดําเนินการต่อ',
    navTo: 'ไปที่เอกสาร',
    sideTipTitle: 'อะไรต่อไป',
    sideTipContent: 'หลังจากที่เอกสารเสร็จสิ้นการจัดทําดัชนี ความรู้สามารถรวมเข้ากับแอปพลิเคชันเป็นบริบท คุณสามารถค้นหาการตั้งค่าบริบทในหน้าการประสานงานพร้อมท์ คุณยังสามารถสร้างเป็นปลั๊กอินการจัดทําดัชนี ChatGPT อิสระสําหรับการเผยแพร่',
    modelTitle: 'คุณแน่ใจหรือว่าจะหยุดฝัง?',
    modelContent: 'หากคุณต้องการดําเนินการต่อในภายหลัง คุณจะดําเนินการต่อจากจุดที่คุณค้างไว้',
    modelButtonConfirm: 'ยืนยัน',
    modelButtonCancel: 'ยกเลิก',
  },
  otherDataSource: {
    learnMore: 'ศึกษาเพิ่มเติม',
    title: 'เชื่อมต่อกับแหล่งข้อมูลอื่นใช่ไหม',
    description: 'ปัจจุบัน ฐานความรู้ของ Dify มีแหล่งข้อมูลที่จํากัดเท่านั้น การมีส่วนร่วมในแหล่งข้อมูลในฐานความรู้ Dify เป็นวิธีที่ยอดเยี่ยมในการช่วยเพิ่มความยืดหยุ่นและพลังของแพลตฟอร์มสําหรับผู้ใช้ทุกคน คู่มือการมีส่วนร่วมของเราทําให้ง่ายต่อการเริ่มต้นใช้งาน โปรดคลิกที่ลิงค์ด้านล่างเพื่อเรียนรู้เพิ่มเติม',
  },
  watercrawl: {
    configWatercrawl: 'กำหนด Watercrawl',
    getApiKeyLinkText: 'รับคีย์ API ของคุณจาก watercrawl.dev',
    apiKeyPlaceholder: 'คีย์ API จาก watercrawl.dev',
  },
}

export default translation
