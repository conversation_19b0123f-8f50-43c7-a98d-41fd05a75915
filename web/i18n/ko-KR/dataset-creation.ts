const translation = {
  steps: {
    header: {
      creation: '지식 생성',
      update: '데이터 추가',
      fallbackRoute: '지식',
    },
    one: '데이터 소스 선택',
    two: '텍스트 전처리 및 클리닝',
    three: '실행 및 완료',
  },
  error: {
    unavailable: '이 지식은 사용할 수 없습니다',
  },
  stepOne: {
    filePreview: '파일 미리보기',
    pagePreview: '페이지 미리보기',
    dataSourceType: {
      file: '텍스트 파일에서 가져오기',
      notion: 'Notion 동기화',
      web: '웹 사이트 동기화',
    },
    uploader: {
      title: '텍스트 파일 업로드',
      button: '파일이나 폴더를 끌어서 놓기',
      browse: '찾아보기',
      tip: '{{supportTypes}}을 (를) 지원합니다. 파일당 최대 크기는 {{size}}MB 입니다.',
      validation: {
        typeError: '지원되지 않는 파일 유형입니다',
        size: '파일 크기가 너무 큽니다. 최대 크기는 {{size}}MB 입니다',
        count: '여러 파일은 지원되지 않습니다',
        filesNumber: '일괄 업로드 제한 ({{filesNumber}}개) 에 도달했습니다.',
      },
      cancel: '취소',
      change: '변경',
      failed: '업로드에 실패했습니다',
    },
    notionSyncTitle: 'Notion 에 연결되지 않았습니다',
    notionSyncTip: 'Notion 과 동기화하려면 먼저 Notion 에 연결해야 합니다.',
    connect: '연결하기',
    button: '다음',
    emptyDatasetCreation: '비어있는 지식 생성',
    modal: {
      title: '비어있는 지식 생성',
      tip: '비어있는 지식에는 문서가 포함되지 않으며 언제든지 문서를 업로드할 수 있습니다.',
      input: '지식 이름',
      placeholder: '입력하세요',
      nameNotEmpty: '이름은 비워둘 수 없습니다',
      nameLengthInvalid: '이름은 1~40 자여야 합니다',
      cancelButton: '취소',
      confirmButton: '생성',
      failed: '생성에 실패했습니다',
    },
    website: {
      limit: '한계',
      options: '옵션',
      firecrawlDoc: 'Firecrawl 문서',
      selectAll: '모두 선택',
      maxDepth: '최대 수심',
      includeOnlyPaths: '경로만 포함',
      excludePaths: '경로 제외',
      preview: '미리 보기',
      run: '달리다',
      fireCrawlNotConfigured: 'Firecrawl 이 구성되지 않았습니다.',
      firecrawlTitle: 'Firecrawl 로 🔥웹 콘텐츠 추출',
      configure: '구성',
      resetAll: '모두 재설정',
      crawlSubPage: '하위 페이지 크롤링',
      exceptionErrorTitle: 'Firecrawl 작업을 실행하는 동안 예외가 발생했습니다.',
      scrapTimeInfo: '{{time}}s 내에 총 {{total}} 페이지를 스크랩했습니다.',
      unknownError: '알 수 없는 오류',
      totalPageScraped: '스크랩한 총 페이지 수:',
      fireCrawlNotConfiguredDescription: 'API 키로 Firecrawl 을 구성하여 사용합니다.',
      extractOnlyMainContent: '기본 콘텐츠만 추출합니다 (머리글, 탐색, 바닥글 등 없음).',
      maxDepthTooltip: '입력한 URL 을 기준으로 크롤링할 최대 수준입니다. 깊이 0 은 입력 된 url 의 페이지를 긁어 내고, 깊이 1 은 url 과 enteredURL + one / 이후의 모든 것을 긁어 모으는 식입니다.',
      chooseProvider: '제공자 선택',
      jinaReaderDocLink: 'https://jina.ai/reader',
      useSitemap: '사이트맵 사용',
      jinaReaderNotConfiguredDescription: '액세스를 위해 무료 API 키를 입력하여 Jina Reader 를 설정합니다.',
      jinaReaderDoc: 'Jina Reader 에 대해 자세히 알아보기',
      jinaReaderTitle: '전체 사이트를 Markdown 으로 변환',
      jinaReaderNotConfigured: 'Jina Reader 가 구성되지 않았습니다.',
      useSitemapTooltip: '사이트맵을 따라 사이트를 크롤링합니다. 그렇지 않은 경우 Jina Reader 는 페이지 관련성에 따라 반복적으로 크롤링하여 더 적지만 더 높은 품질의 페이지를 생성합니다.',
      watercrawlDoc: '워터크롤 문서',
      waterCrawlNotConfiguredDescription: 'API 키로 Watercrawl 을 구성하여 사용하십시오.',
      watercrawlTitle: 'Watercrawl 로 웹 콘텐츠 추출하기',
      configureFirecrawl: '파이어크롤 구성하기',
      configureJinaReader: '지나 리더 설정하기',
      waterCrawlNotConfigured: 'Watercrawl 이 설정되어 있지 않습니다.',
      configureWatercrawl: '워터크롤 구성하기',
    },
    cancel: '취소',
  },
  stepTwo: {
    segmentation: '청크 설정',
    auto: '자동',
    autoDescription: '청크 및 전처리 규칙을 자동으로 설정합니다. 처음 사용자는 이 옵션을 선택하는 것을 권장합니다.',
    custom: '사용자 설정',
    customDescription: '청크 규칙, 청크 길이, 전처리 규칙 등을 사용자 정의합니다.',
    separator: '세그먼트 식별자',
    separatorPlaceholder: '예: 줄바꿈 (\\\\n) 또는 특수 구분자 (예: "***")',
    maxLength: '최대 청크 길이',
    overlap: '청크 중첩',
    overlapTip: '청크 중첩을 설정하여 그 사이의 의미적 연관성을 유지하고 검색 효과를 향상시킬 수 있습니다. 최대 청크 크기의 10%~25% 로 설정하는 것이 좋습니다.',
    overlapCheck: '청크 중첩은 최대 청크 길이를 초과할 수 없습니다',
    rules: '텍스트 전처리 규칙',
    removeExtraSpaces: '연속된 공백, 줄바꿈, 탭을 대체합니다',
    removeUrlEmails: '모든 URL 과 이메일 주소를 제거합니다',
    removeStopwords: '일반적인 불용어 (예: "a", "an", "the" 등) 를 제거합니다',
    preview: '미리보기',
    reset: '초기화',
    indexMode: '인덱스 모드',
    qualified: '고품질',
    recommend: '추천',
    qualifiedTip: '사용자 쿼리에 대해 더 높은 정확성을 제공하기 위해 기본 시스템 임베딩 인터페이스를 호출하여 처리합니다.',
    warning: '모델 제공자의 API 키를 설정하세요.',
    click: '설정으로 이동',
    economical: '경제적',
    economicalTip: '오프라인 벡터 엔진, 키워드 인덱스 등을 사용하여 토큰 소비 없이 정확도를 낮춥니다.',
    QATitle: '질문과 답변 형식으로 세그먼트화',
    QATip: '이 옵션을 활성화하면 추가 토큰이 소비됩니다',
    QALanguage: '사용 언어',
    estimateCost: '예상 비용',
    estimateSegment: '예상 청크 수',
    segmentCount: '청크',
    calculating: '계산 중...',
    fileSource: '문서 전처리',
    notionSource: '페이지 전처리',
    other: '기타',
    fileUnit: '파일',
    notionUnit: '페이지',
    previousStep: '이전 단계',
    nextStep: '저장하고 처리',
    save: '저장하고 처리',
    cancel: '취소',
    sideTipTitle: '청크와 전처리가 필요한 이유',
    sideTipP1: '텍스트 데이터를 처리할 때 청크와 클리닝은 두 가지 중요한 전처리 단계입니다.',
    sideTipP2: '세그멘테이션은 긴 텍스트를 단락으로 분할하여 모델이 이해하기 쉽게 합니다. 이로 인해 모델 결과의 품질과 관련성이 향상됩니다.',
    sideTipP3: '클리닝은 불필요한 문자 및 형식을 제거하여 지식을 더 깔끔하고 분석 가능한 것으로 만듭니다.',
    sideTipP4: '적절한 청크와 클리닝은 모델의 성능을 향상시키고 정확하고 가치 있는 결과를 제공합니다.',
    previewTitle: '미리보기',
    previewTitleButton: '미리보기',
    previewButton: '질문 - 답변 형식으로 전환',
    previewSwitchTipStart: '현재 청크 미리보기는 텍스트 형식입니다. 질문과 답변 형식 미리보기로 전환하면',
    previewSwitchTipEnd: ' 추가 토큰이 소비됩니다',
    characters: '문자',
    indexSettingTip: '인덱스 방식을 변경하려면,',
    retrievalSettingTip: '인덱스 방식을 변경하려면,',
    datasetSettingLink: '지식 설정',
    webpageUnit: '페이지',
    websiteSource: '웹 사이트 전처리',
    separatorTip: '구분 기호는 텍스트를 구분하는 데 사용되는 문자입니다. \\n\\n 및 \\n은 단락과 줄을 구분하는 데 일반적으로 사용되는 구분 기호입니다. 쉼표 (\\n\\n,\\n) 와 함께 사용하면 최대 청크 길이를 초과할 경우 단락이 줄로 분할됩니다. 직접 정의한 특수 구분 기호 (예: ***) 를 사용할 수도 있습니다.',
    maxLengthCheck: '최대 청크 길이는 {{limit}} 미만이어야 합니다.',
    childChunkForRetrieval: '검색을 위한 자식 청크',
    qaSwitchHighQualityTipContent: '현재 고품질 인덱스 방법만 Q&A 형식 청크를 지원합니다. 고화질 모드로 전환하시겠습니까?',
    previewChunkTip: '왼쪽의 \'Preview Chunk\' 버튼을 클릭하여 프리뷰를 로드합니다',
    general: '일반',
    fullDoc: '전체 문서',
    previewChunk: '프리뷰 청크 (Preview Chunk)',
    parentChunkForContext: '컨텍스트에 대한 Parent-chunk',
    parentChildDelimiterTip: '구분 기호는 텍스트를 구분하는 데 사용되는 문자입니다. \\n\\n은 원본 문서를 큰 부모 청크로 분할하는 데 권장됩니다. 직접 정의한 특수 구분 기호를 사용할 수도 있습니다.',
    paragraph: '단락',
    parentChild: '부모 - 자식',
    useQALanguage: 'Q&A 형식을 사용하는 청크',
    highQualityTip: '고품질 모드에서 삽입을 마치면 경제적 모드로 되돌릴 수 없습니다.',
    notAvailableForQA: 'Q&A 인덱스에는 사용할 수 없습니다.',
    qaSwitchHighQualityTipTitle: 'Q&A 형식에는 고품질 인덱싱 방법이 필요합니다.',
    notAvailableForParentChild: '부모 - 자식 인덱스에는 사용할 수 없습니다.',
    previewChunkCount: '{{개수}} 추정된 청크',
    parentChildTip: '부모 - 자식 모드를 사용할 때 자식 청크는 검색에 사용되고 부모 청크는 컨텍스트로 회수에 사용됩니다.',
    generalTip: '일반적인 텍스트 청크 모드에서는 검색된 청크와 회수된 청크가 동일합니다.',
    fullDocTip: '전체 문서가 상위 청크로 사용되며 직접 검색됩니다. 성능상의 이유로 10000 토큰을 초과하는 텍스트는 자동으로 잘립니다.',
    parentChildChunkDelimiterTip: '구분 기호는 텍스트를 구분하는 데 사용되는 문자입니다. \\n 은 부모 청크를 작은 자식 청크로 분할하는 데 권장됩니다. 직접 정의한 특수 구분 기호를 사용할 수도 있습니다.',
    switch: '스위치',
    paragraphTip: '이 모드는 구분 기호와 최대 청크 길이에 따라 텍스트를 단락으로 분할하며, 분할된 텍스트를 검색을 위한 부모 청크로 사용합니다.',
  },
  stepThree: {
    creationTitle: '🎉 지식이 생성되었습니다',
    creationContent: '지식 이름이 자동으로 설정되었지만 언제든지 변경할 수 있습니다',
    label: '지식 이름',
    additionTitle: '🎉 문서가 업로드되었습니다',
    additionP1: '문서가 지식에 업로드되었습니다',
    additionP2: '지식의 문서 목록에서 찾을 수 있습니다.',
    stop: '처리 중지',
    resume: '처리 재개',
    navTo: '문서로 이동',
    sideTipTitle: '다음 단계는 무엇인가요',
    sideTipContent:
      '문서 인덱싱이 완료되면 지식을 응용 프로그램 컨텍스트로 통합할 수 있습니다. 프롬프트 오케스트레이션 페이지에서 컨텍스트 설정을 찾을 수 있습니다. 또한 독립된 ChatGPT 인덱스 플러그인으로 출시할 수도 있습니다.',
    modelTitle: '임베딩을 중지해도 괜찮습니까?',
    modelContent: '나중에 처리를 다시 시작해야 할 경우, 중단한 위치에서 계속합니다.',
    modelButtonConfirm: '확인',
    modelButtonCancel: '취소',
  },
  firecrawl: {
    getApiKeyLinkText: 'firecrawl.dev 에서 API 키 가져오기',
    apiKeyPlaceholder: 'firecrawl.dev 의 API 키',
    configFirecrawl: 'Firecrawl 구성 🔥',
  },
  jinaReader: {
    apiKeyPlaceholder: 'jina.ai 의 API 키',
    getApiKeyLinkText: 'jina.ai 에서 무료 API 키 받기',
    configJinaReader: 'Jina Reader 구성',
  },
  otherDataSource: {
    learnMore: '더 알아보세요',
    title: '다른 데이터 소스에 연결하시겠습니까?',
    description: '현재 Dify 의 기술 자료에는 제한된 데이터 소스만 있습니다. Dify 기술 자료에 데이터 소스를 제공하는 것은 모든 사용자를 위해 플랫폼의 유연성과 기능을 향상시키는 데 도움이 되는 환상적인 방법입니다. 기여 가이드를 통해 쉽게 시작할 수 있습니다. 자세한 내용은 아래 링크를 클릭하십시오.',
  },
  watercrawl: {
    getApiKeyLinkText: 'watercrawl.dev 에서 API 키를 얻으세요.',
    configWatercrawl: '워터크롤 구성하기',
    apiKeyPlaceholder: 'watercrawl.dev 의 API 키',
  },
}

export default translation
