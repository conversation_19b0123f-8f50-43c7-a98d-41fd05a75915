const translation = {
  category: {
    extensions: '<PERSON><PERSON><PERSON><PERSON><PERSON>ia',
    agents: 'Strategie agentów',
    bundles: 'Wi<PERSON><PERSON><PERSON>',
    all: 'Cały',
    tools: 'Narzęd<PERSON>',
    models: 'Modele',
  },
  categorySingle: {
    model: 'Model',
    extension: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    bundle: 'Pak<PERSON>',
    agent: 'Strategia agenta',
    tool: 'Narz<PERSON><PERSON><PERSON>',
  },
  list: {
    source: {
      marketplace: 'Instalowanie z Marketplace',
      github: 'Instalowanie z usługi GitHub',
      local: 'Zainstaluj z lokalnego pliku pakietu',
    },
    notFound: 'Nie znaleziono wtyczek',
    noInstalled: 'Brak zainstalowanych wtyczek',
  },
  source: {
    github: 'Usługa GitHub',
    local: 'Lokalny plik pakietu',
    marketplace: 'Rynek',
  },
  detailPanel: {
    categoryTip: {
      local: 'Wtyczka lokalna',
      github: 'Zainstalowany z Github',
      marketplace: 'Zainstalowano z witryny Marketplace',
      debugging: '<PERSON>tycz<PERSON> do debugowania',
    },
    operation: {
      remove: '<PERSON><PERSON><PERSON><PERSON>',
      checkUpdate: 'Sprawdź aktualizację',
      detail: 'Szczegóły',
      update: 'Aktualizacja',
      install: 'Instalować',
      viewDetail: 'Pokaż szczegóły',
      info: 'Informacje o wtyczce',
    },
    toolSelector: {
      unsupportedContent2: 'Kliknij, aby zmienić wersję.',
      uninstalledLink: 'Zarządzanie we wtyczkach',
      placeholder: 'Wybierz narzędzie...',
      paramsTip1: 'Steruje parametrami wnioskowania LLM.',
      unsupportedContent: 'Zainstalowana wersja wtyczki nie zapewnia tej akcji.',
      params: 'KONFIGURACJA ROZUMOWANIA',
      auto: 'Automatyczne',
      empty: 'Kliknij przycisk "+", aby dodać narzędzia. Możesz dodać wiele narzędzi.',
      descriptionLabel: 'Opis narzędzia',
      title: 'Dodaj narzędzie',
      descriptionPlaceholder: 'Krótki opis przeznaczenia narzędzia, np. zmierzenie temperatury dla konkretnej lokalizacji.',
      settings: 'USTAWIENIA UŻYTKOWNIKA',
      uninstalledContent: 'Ta wtyczka jest instalowana z repozytorium lokalnego/GitHub. Proszę użyć po instalacji.',
      unsupportedTitle: 'Nieobsługiwana akcja',
      uninstalledTitle: 'Narzędzie nie jest zainstalowane',
      paramsTip2: 'Gdy opcja "Automatycznie" jest wyłączona, używana jest wartość domyślna.',
      toolLabel: 'Narzędzie',
      toolSetting: 'Ustawienia narzędzi',
    },
    strategyNum: '{{liczba}} {{strategia}} ZAWARTE',
    endpointsEmpty: 'Kliknij przycisk "+", aby dodać punkt końcowy',
    endpointDisableTip: 'Wyłącz punkt końcowy',
    endpoints: 'Punkty końcowe',
    disabled: 'Niepełnosprawny',
    endpointModalTitle: 'Punkt końcowy konfiguracji',
    endpointsDocLink: 'Wyświetlanie dokumentu',
    endpointDeleteTip: 'Usuń punkt końcowy',
    actionNum: '{{liczba}} {{akcja}} ZAWARTE',
    configureTool: 'Narzędzie konfiguracji',
    configureModel: 'Konfiguracja modelu',
    switchVersion: 'Wersja przełącznika',
    serviceOk: 'Serwis OK',
    configureApp: 'Konfiguracja aplikacji',
    endpointModalDesc: 'Po skonfigurowaniu można korzystać z funkcji dostarczanych przez wtyczkę za pośrednictwem punktów końcowych API.',
    endpointDisableContent: 'Czy chcesz wyłączyć {{name}}?',
    endpointDeleteContent: 'Czy chcesz usunąć {{name}}?',
    endpointsTip: 'Ta wtyczka zapewnia określone funkcje za pośrednictwem punktów końcowych i można skonfigurować wiele zestawów punktów końcowych dla bieżącego obszaru roboczego.',
    modelNum: '{{liczba}} MODELE W ZESTAWIE',
  },
  debugInfo: {
    viewDocs: 'Wyświetlanie dokumentów',
    title: 'Debugowanie',
  },
  privilege: {
    everyone: 'Każdy',
    whoCanDebug: 'Kto może debugować wtyczki?',
    admins: 'Administratorzy',
    noone: 'Nikt',
    whoCanInstall: 'Kto może instalować wtyczki i nimi zarządzać?',
    title: 'Preferencje wtyczek',
  },
  pluginInfoModal: {
    packageName: 'Pakiet',
    title: 'Informacje o wtyczce',
    release: 'Zwolnić',
    repository: 'Repozytorium',
  },
  action: {
    deleteContentLeft: 'Czy chcesz usunąć',
    delete: 'Usuń wtyczkę',
    pluginInfo: 'Informacje o wtyczce',
    checkForUpdates: 'Sprawdź dostępność aktualizacji',
    usedInApps: 'Ta wtyczka jest używana w aplikacjach {{num}}.',
    deleteContentRight: 'wtyczka?',
  },
  installModal: {
    labels: {
      package: 'Pakiet',
      repository: 'Repozytorium',
      version: 'Wersja',
    },
    installPlugin: 'Zainstaluj wtyczkę',
    install: 'Instalować',
    installFailedDesc: 'Instalacja wtyczki nie powiodła się.',
    installedSuccessfullyDesc: 'Wtyczka została pomyślnie zainstalowana.',
    back: 'Wstecz',
    readyToInstallPackages: 'Informacje o instalacji następujących wtyczek {{num}}',
    cancel: 'Anuluj',
    pluginLoadError: 'Błąd ładowania wtyczki',
    installing: 'Instalowanie...',
    installFailed: 'Instalacja nie powiodła się',
    installComplete: 'Instalacja zakończona',
    readyToInstall: 'Informacje o instalacji następującej wtyczki',
    dropPluginToInstall: 'Upuść pakiet wtyczek tutaj, aby zainstalować',
    uploadFailed: 'Przekazywanie nie powiodło się',
    next: 'Następny',
    fromTrustSource: 'Upewnij się, że instalujesz wtyczki tylko z <trustSource>zaufanego źródła</trustSource>.',
    pluginLoadErrorDesc: 'Ta wtyczka nie zostanie zainstalowana',
    close: 'Zamykać',
    readyToInstallPackage: 'Informacje o instalacji następującej wtyczki',
    uploadingPackage: 'Przesyłanie {{packageName}}...',
    installedSuccessfully: 'Instalacja powiodła się',
    installWarning: 'Ten plugin nie może być zainstalowany.',
  },
  installFromGitHub: {
    installPlugin: 'Zainstaluj wtyczkę z GitHub',
    selectVersionPlaceholder: 'Proszę wybrać wersję',
    gitHubRepo: 'Repozytorium GitHub',
    uploadFailed: 'Przekazywanie nie powiodło się',
    selectVersion: 'Wybierz wersję',
    installFailed: 'Instalacja nie powiodła się',
    updatePlugin: 'Zaktualizuj wtyczkę z GitHub',
    selectPackagePlaceholder: 'Proszę wybrać pakiet',
    selectPackage: 'Wybierz pakiet',
    installedSuccessfully: 'Instalacja powiodła się',
    installNote: 'Upewnij się, że instalujesz wtyczki tylko z zaufanego źródła.',
  },
  upgrade: {
    successfulTitle: 'Instalacja powiodła się',
    description: 'Informacje o instalacji następującej wtyczki',
    close: 'Zamykać',
    upgrade: 'Instalować',
    title: 'Zainstaluj wtyczkę',
    upgrading: 'Instalowanie...',
    usedInApps: 'Używane w aplikacjach {{num}}',
  },
  error: {
    inValidGitHubUrl: 'Nieprawidłowy adres URL usługi GitHub. Podaj prawidłowy adres URL w formacie: https://github.com/owner/repo',
    noReleasesFound: 'Nie znaleziono wydań. Sprawdź repozytorium GitHub lub wejściowy adres URL.',
    fetchReleasesError: 'Nie można pobrać wydań. Spróbuj ponownie później.',
  },
  marketplace: {
    sortOption: {
      newlyReleased: 'Nowo wydany',
      firstReleased: 'Po raz pierwszy wydany',
      recentlyUpdated: 'Ostatnio zaktualizowane',
      mostPopular: 'Najpopularniejsze',
    },
    sortBy: 'Czarne miasto',
    discover: 'Odkryć',
    moreFrom: 'Więcej z Marketplace',
    empower: 'Zwiększ możliwości rozwoju sztucznej inteligencji',
    viewMore: 'Zobacz więcej',
    and: 'i',
    difyMarketplace: 'Rynek Dify',
    noPluginFound: 'Nie znaleziono wtyczki',
    pluginsResult: '{{num}} wyniki',
    partnerTip: 'Zweryfikowane przez partnera Dify',
    verifiedTip: 'Zweryfikowane przez Dify',
  },
  task: {
    installError: 'Nie udało się zainstalować wtyczek {{errorLength}}, kliknij, aby wyświetlić',
    installedError: 'Nie udało się zainstalować wtyczek {{errorLength}}',
    installing: 'Instalowanie wtyczek {{installingLength}}, 0 gotowe.',
    installingWithSuccess: 'Instalacja wtyczek {{installingLength}}, {{successLength}} powodzenie.',
    clearAll: 'Wyczyść wszystko',
    installingWithError: 'Instalacja wtyczek {{installingLength}}, {{successLength}} powodzenie, {{errorLength}} niepowodzenie',
  },
  search: 'Szukać',
  installFrom: 'ZAINSTALUJ Z',
  searchCategories: 'Kategorie wyszukiwania',
  allCategories: 'Wszystkie kategorie',
  findMoreInMarketplace: 'Więcej informacji w Marketplace',
  searchInMarketplace: 'Wyszukiwanie w Marketplace',
  endpointsEnabled: '{{num}} włączone zestawy punktów końcowych',
  install: '{{num}} instalacji',
  installAction: 'Instalować',
  installPlugin: 'Zainstaluj wtyczkę',
  from: 'Z',
  fromMarketplace: 'Z Marketplace',
  searchPlugins: 'Wtyczki wyszukiwania',
  searchTools: 'Narzędzia wyszukiwania...',
  metadata: {
    title: 'Wtyczki',
  },
  difyVersionNotCompatible: 'Obecna wersja Dify nie jest kompatybilna z tym wtyczką, proszę zaktualizować do minimalnej wymaganej wersji: {{minimalDifyVersion}}',
  requestAPlugin: 'Poproś o wtyczkę',
  publishPlugins: 'Publikowanie wtyczek',
}

export default translation
