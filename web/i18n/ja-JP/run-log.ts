const translation = {
  input: '入力',
  result: '結果',
  detail: '詳細情報',
  tracing: '実行追跡',
  resultPanel: {
    status: 'ステータス',
    time: '処理時間',
    tokens: 'トークン総数',
  },
  meta: {
    title: 'メタデータ',
    status: '状態',
    version: 'バージョン',
    executor: '実行者',
    startTime: '開始時刻',
    time: '総処理時間',
    tokens: 'トークン総数',
    steps: '処理ステップ数',
  },
  resultEmpty: {
    title: '今回の実行では JSON 形式のみが出力されました',
    tipLeft: '詳細を確認するには',
    link: '詳細情報パネル',
    tipRight: 'へ移動してください',
  },
  actionLogs: '操作ログ',
  circularInvocationTip: '現在のワークフローにツール/ノードの循環呼び出しが検出されました',
}

export default translation
