const translation = {
  toVerified: 'Get Education Verified',
  toVerifiedTip: {
    front: 'You are now eligible for Education Verified status. Please enter your education information below to complete the process and receive an',
    coupon: 'exclusive 100% coupon',
    end: 'for the Dify Professional Plan.',
  },
  currentSigned: 'CURRENTLY SIGNED IN AS',
  form: {
    schoolName: {
      title: 'Your School Name',
      placeholder: 'Enter the official, unabbreviated name of your school',
    },
    schoolRole: {
      title: 'Your School Role',
      option: {
        student: 'Student',
        teacher: 'Teacher',
        administrator: 'School Administrator',
      },
    },
    terms: {
      title: 'Terms & Agreements',
      desc: {
        front: 'Your information and use of Education Verified status are subject to our',
        and: 'and',
        end: '. By submitting:',
        termsOfService: 'Terms of Service',
        privacyPolicy: 'Privacy Policy',
      },
      option: {
        age: 'I confirm I am at least 18 years old',
        inSchool: 'I confirm I am enrolled or employed at the institution provided. Dify may request proof of enrollment/employment. If I misrepresent my eligibility, I agree to pay any fees initially waived based on my education status.',
      },
    },
  },
  submit: 'Submit',
  submitError: 'Form submission failed. Please try again later.',
  learn: 'Learn how to get education verified',
  successTitle: 'You Have Got Dify Education Verified',
  successContent: 'We have issued a 100% discount coupon for the Dify Professional plan to your account. The coupon is valid for one year, please use it within the validity period.',
  rejectTitle: 'Your Dify Educational Verification Has Been Rejected',
  rejectContent: 'Unfortunately, you are not eligible for Education Verified status and therefore cannot receive the exclusive 100% coupon for the Dify Professional Plan if you use this email address.',
  emailLabel: 'Your current email',
}

export default translation
