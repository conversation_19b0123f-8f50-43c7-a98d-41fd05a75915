const translation = {
  createApp: 'CREAR APP',
  types: {
    all: 'Todos',
    chatbot: 'Chatbot',
    agent: 'Agent<PERSON>',
    workflow: 'Flujo de trabajo',
    completion: 'Finalización',
    basic: 'Básico',
    advanced: 'Flujo de chat',
  },
  duplicate: 'Duplicar',
  duplicateTitle: 'Duplicar App',
  export: 'Exportar DSL',
  exportFailed: 'Error al exportar DSL.',
  importDSL: 'Importar archivo DSL',
  createFromConfigFile: 'Crear desde archivo DSL',
  deleteAppConfirmTitle: '¿Eliminar esta app?',
  deleteAppConfirmContent:
    'Eliminar la app es irreversible. Los usuarios ya no podrán acceder a tu app y todas las configuraciones y registros de prompts se eliminarán permanentemente.',
  appDeleted: 'App eliminada',
  appDeleteFailed: 'Error al eliminar app',
  join: 'Únete a la comunidad',
  communityIntro:
    'Discute con miembros del equipo, colaboradores y desarrolladores en diferentes canales.',
  roadmap: 'Ver nuestro plan de desarrollo',
  newApp: {
    startFromBlank: 'Crear desde cero',
    startFromTemplate: 'Crear desde plantilla',
    captionAppType: '¿Qué tipo de app quieres crear?',
    chatbotDescription: 'Crea una aplicación basada en chat. Esta app utiliza un formato de pregunta y respuesta, permitiendo múltiples rondas de conversación continua.',
    completionDescription: 'Crea una aplicación que genera texto de alta calidad basado en prompts, como la generación de artículos, resúmenes, traducciones y más.',
    completionWarning: 'Este tipo de app ya no será compatible.',
    agentDescription: 'Crea un Agente inteligente que puede elegir herramientas de forma autónoma para completar tareas',
    workflowDescription: 'Crea una aplicación que genera texto de alta calidad basado en flujos de trabajo con un alto grado de personalización. Es adecuado para usuarios experimentados.',
    workflowWarning: 'Actualmente en beta',
    chatbotType: 'Método de orquestación del Chatbot',
    basic: 'Básico',
    basicTip: 'Para principiantes, se puede cambiar a Chatflow más adelante',
    basicFor: 'PARA PRINCIPIANTES',
    basicDescription: 'La Orquestación Básica permite la orquestación de una app de Chatbot utilizando configuraciones simples, sin la capacidad de modificar los prompts incorporados. Es adecuado para principiantes.',
    advanced: 'Chatflow',
    advancedFor: 'Para usuarios avanzados',
    advancedDescription: 'La Orquestación de Flujo de Trabajo orquesta Chatbots en forma de flujos de trabajo, ofreciendo un alto grado de personalización, incluida la capacidad de editar los prompts incorporados. Es adecuado para usuarios experimentados.',
    captionName: 'Icono y nombre de la app',
    appNamePlaceholder: 'Asigna un nombre a tu app',
    captionDescription: 'Descripción',
    appDescriptionPlaceholder: 'Ingresa la descripción de la app',
    useTemplate: 'Usar esta plantilla',
    previewDemo: 'Vista previa de demostración',
    chatApp: 'Asistente',
    chatAppIntro:
      'Quiero construir una aplicación basada en chat. Esta app utiliza un formato de pregunta y respuesta, permitiendo múltiples rondas de conversación continua.',
    agentAssistant: 'Nuevo Asistente de Agente',
    completeApp: 'Generador de Texto',
    completeAppIntro:
      'Quiero crear una aplicación que genera texto de alta calidad basado en prompts, como la generación de artículos, resúmenes, traducciones y más.',
    showTemplates: 'Quiero elegir una plantilla',
    hideTemplates: 'Volver a la selección de modo',
    Create: 'Crear',
    Cancel: 'Cancelar',
    nameNotEmpty: 'El nombre no puede estar vacío',
    appTemplateNotSelected: 'Por favor, selecciona una plantilla',
    appTypeRequired: 'Por favor, selecciona un tipo de app',
    appCreated: 'App creada',
    appCreateFailed: 'Error al crear app',
    Confirm: 'Confirmar',
    caution: 'Cautela',
    appCreateDSLErrorTitle: 'Incompatibilidad de versiones',
    appCreateDSLErrorPart2: '¿Quieres continuar?',
    appCreateDSLErrorPart4: 'Versión de DSL compatible con el sistema:',
    appCreateDSLErrorPart1: 'Se ha detectado una diferencia significativa en las versiones de DSL. Forzar la importación puede hacer que la aplicación no funcione correctamente.',
    appCreateDSLWarning: 'Precaución: La diferencia de versión de DSL puede afectar a determinadas funciones',
    appCreateDSLErrorPart3: 'Versión actual de DSL de la aplicación:',
    forBeginners: 'Tipos de aplicación más básicos',
    learnMore: 'Aprende más',
    noTemplateFoundTip: 'Intente buscar usando diferentes palabras clave.',
    chatbotShortDescription: 'Chatbot basado en LLM con una configuración sencilla',
    chooseAppType: 'Elija un tipo de aplicación',
    noAppsFound: 'No se han encontrado aplicaciones',
    workflowUserDescription: 'Construya flujos de trabajo autónomos de IA con la simplicidad de arrastrar y soltar.',
    advancedShortDescription: 'Flujo de trabajo mejorado para chats de múltiples turnos',
    forAdvanced: 'PARA USUARIOS AVANZADOS',
    completionShortDescription: 'Asistente de IA para tareas de generación de texto',
    optional: 'Opcional',
    noIdeaTip: '¿No tienes ideas? Echa un vistazo a nuestras plantillas',
    agentUserDescription: 'Un agente inteligente capaz de realizar un razonamiento iterativo y un uso autónomo de las herramientas para alcanzar los objetivos de las tareas.',
    workflowShortDescription: 'Flujo agéntico para automatizaciones inteligentes',
    advancedUserDescription: 'Flujo de trabajo con funciones de memoria y una interfaz de chatbot.',
    agentShortDescription: 'Agente inteligente con razonamiento y uso autónomo de herramientas',
    foundResults: '{{conteo}} Resultados',
    noTemplateFound: 'No se han encontrado plantillas',
    foundResult: '{{conteo}} Resultado',
    chatbotUserDescription: 'Cree rápidamente un chatbot basado en LLM con una configuración sencilla. Puedes cambiar a Chatflow más tarde.',
    completionUserDescription: 'Cree rápidamente un asistente de IA para tareas de generación de texto con una configuración sencilla.',
    dropDSLToCreateApp: 'Suelta el archivo DSL aquí para crear la aplicación',
  },
  editApp: 'Editar información',
  editAppTitle: 'Editar información de la app',
  editDone: 'Información de la app actualizada',
  editFailed: 'Error al actualizar información de la app',
  iconPicker: {
    ok: 'OK',
    cancel: 'Cancelar',
    emoji: 'Emoji',
    image: 'Imagen',
  },
  switch: 'Cambiar a Orquestación de Flujo de Trabajo',
  switchTipStart: 'Se creará una nueva copia de la app para ti y la nueva copia cambiará a Orquestación de Flujo de Trabajo. La nueva copia no permitirá',
  switchTip: 'volver',
  switchTipEnd: ' a la Orquestación Básica.',
  switchLabel: 'La copia de la app a crear',
  removeOriginal: 'Eliminar la app original',
  switchStart: 'Iniciar cambio',
  typeSelector: {
    all: 'Todos los tipos',
    chatbot: 'Chatbot',
    agent: 'Agente',
    workflow: 'Flujo de trabajo',
    completion: 'Finalización',
    advanced: 'Flujo de chat',
  },
  tracing: {
    title: 'Rastreo del rendimiento de la app',
    description: 'Configuración de un proveedor de LLMOps de terceros y rastreo del rendimiento de la app.',
    config: 'Configurar',
    collapse: 'Contraer',
    expand: 'Expandir',
    tracing: 'Rastreo',
    disabled: 'Deshabilitado',
    disabledTip: 'Por favor, configura el proveedor primero',
    enabled: 'En servicio',
    tracingDescription: 'Captura el contexto completo de la ejecución de la app, incluyendo llamadas LLM, contexto, prompts, solicitudes HTTP y más, en una plataforma de rastreo de terceros.',
    configProviderTitle: {
      configured: 'Configurado',
      notConfigured: 'Configurar proveedor para habilitar el rastreo',
      moreProvider: 'Más proveedores',
    },
    arize: {
      title: 'Arize',
      description: 'Observabilidad de LLM de nivel empresarial, evaluación en línea y fuera de línea, monitoreo y experimentación—impulsada por OpenTelemetry. Diseñada específicamente para aplicaciones impulsadas por LLM y agentes.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Plataforma de observabilidad, evaluación, ingeniería de prompts y experimentación de código abierto basada en OpenTelemetry para sus flujos de trabajo y agentes de LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description: 'Una plataforma de desarrollo todo en uno para cada paso del ciclo de vida de la aplicación impulsada por LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description: 'Rastrea, evalúa, gestiona prompts y métricas para depurar y mejorar tu aplicación LLM.',
    },
    inUse: 'En uso',
    configProvider: {
      title: 'Configurar ',
      placeholder: 'Ingresa tu {{key}}',
      project: 'Proyecto',
      publicKey: 'Clave pública',
      secretKey: 'Clave secreta',
      viewDocsLink: 'Ver documentación de {{key}}',
      removeConfirmTitle: '¿Eliminar la configuración de {{key}}?',
      removeConfirmContent: 'La configuración actual está en uso, eliminarla desactivará la función de rastreo.',
    },
    view: 'Vista',
    opik: {
      description: 'Opik es una plataforma de código abierto para evaluar, probar y monitorear aplicaciones LLM.',
      title: 'Opik',
    },
    weave: {
      description: 'Weave es una plataforma de código abierto para evaluar, probar y monitorear aplicaciones de LLM.',
      title: 'Tejer',
    },
  },
  answerIcon: {
    title: 'Usar el icono de la aplicación web para reemplazar 🤖',
    descriptionInExplore: 'Si se debe usar el icono de la aplicación web para reemplazarlo 🤖 en Explore',
    description: 'Si se va a usar el icono de la aplicación web para reemplazarlo 🤖 en la aplicación compartida',
  },
  importFromDSLUrl: 'URL de origen',
  importFromDSLUrlPlaceholder: 'Pegar enlace DSL aquí',
  importFromDSL: 'Importar desde DSL',
  importFromDSLFile: 'Desde el archivo DSL',
  mermaid: {
    handDrawn: 'Dibujado a mano',
    classic: 'Clásico',
  },
  openInExplore: 'Abrir en Explorar',
  newAppFromTemplate: {
    sidebar: {
      Programming: 'Programación',
      Agent: 'Agente',
      Writing: 'Escritura',
      Assistant: 'Asistente',
      Recommended: 'Recomendado',
      HR: 'HR',
      Workflow: 'Flujo de trabajo',
    },
    byCategories: 'POR CATEGORÍAS',
    searchAllTemplate: 'Buscar todas las plantillas...',
  },
  showMyCreatedAppsOnly: 'Mostrar solo mis aplicaciones creadas',
  appSelector: {
    label: 'APLICACIÓN',
    placeholder: 'Selecciona una aplicación...',
    noParams: 'No se necesitan parámetros',
    params: 'PARÁMETROS DE LA APLICACIÓN',
  },
  structOutput: {
    notConfiguredTip: 'La salida estructurada aún no ha sido configurada.',
    required: 'Requerido',
    configure: 'Configurar',
    LLMResponse: 'Respuesta del LLM',
    moreFillTip: 'Mostrando un máximo de 10 niveles de anidación',
    modelNotSupportedTip: 'El modelo actual no admite esta función y se degrada automáticamente a inyección de comandos.',
    structuredTip: 'Las Salidas Estructuradas son una función que garantiza que el modelo siempre generará respuestas que se ajusten a su esquema JSON proporcionado.',
    modelNotSupported: 'Modelo no soportado',
    structured: 'sistemático',
  },
  accessItemsDescription: {
    anyone: 'Cualquiera puede acceder a la aplicación web.',
    specific: 'Solo grupos o miembros específicos pueden acceder a la aplicación web',
    organization: 'Cualquiera en la organización puede acceder a la aplicación web',
    external: 'Solo los usuarios externos autenticados pueden acceder a la aplicación web.',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Cualquiera con el enlace',
      specific: 'Grupos o miembros específicos',
      organization: 'Solo miembros dentro de la empresa',
      external: 'Usuarios externos autenticados',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Buscar grupos y miembros',
      allMembers: 'Todos los miembros',
      expand: 'Expandir',
      noResult: 'Sin resultado',
    },
    title: 'Control de Acceso a la Aplicación Web',
    description: 'Establecer permisos de acceso a la aplicación web',
    accessLabel: '¿Quién tiene acceso?',
    groups_one: '{{count}} GRUPO',
    groups_other: '{{count}} GRUPOS',
    members_one: '{{count}} MIEMBRO',
    members_other: '{{count}} MIEMBROS',
    noGroupsOrMembers: 'No grupos o miembros seleccionados',
    webAppSSONotEnabledTip: 'Por favor, contacte al administrador de la empresa para configurar el método de autenticación de la aplicación web.',
    updateSuccess: 'Actualización exitosa',
  },
  publishApp: {
    title: '¿Quién puede acceder a la aplicación web?',
    notSet: 'No establecido',
    notSetDesc: 'Actualmente nadie puede acceder a la aplicación web. Por favor, configure los permisos.',
  },
  accessControl: 'Control de Acceso a la Aplicación Web',
  noAccessPermission: 'No se permite el acceso a la aplicación web',
}

export default translation
