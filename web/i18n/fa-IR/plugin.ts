const translation = {
  category: {
    all: 'همه',
    models: 'مدل',
    bundles: 'بسته',
    agents: 'استراتژی های عامل',
    tools: 'ابزار',
    extensions: 'پسوند',
  },
  categorySingle: {
    tool: 'ابزار',
    agent: 'استراتژی نمایندگی',
    extension: 'فرمت',
    model: 'مدل',
    bundle: 'بسته',
  },
  list: {
    source: {
      marketplace: 'از Marketplace نصب کنید',
      github: 'نصب از GitHub',
      local: 'نصب از فایل بسته محلی',
    },
    notFound: 'هیچ افزونه ای یافت نشد',
    noInstalled: 'هیچ افزونه ای نصب نشده است',
  },
  source: {
    github: 'گیت‌هاب',
    marketplace: 'بازار',
    local: 'فایل بسته محلی',
  },
  detailPanel: {
    categoryTip: {
      debugging: 'اشکال زدایی پلاگین',
      marketplace: 'نصب شده از Marketplace',
      local: 'پلاگین محلی',
      github: 'نصب شده از Github',
    },
    operation: {
      checkUpdate: 'به روز رسانی را بررسی کنید',
      info: 'اطلاعات پلاگین',
      remove: 'حذف',
      update: 'روز رسانی',
      detail: 'جزئیات',
      viewDetail: 'نمایش جزئیات',
      install: 'نصب',
    },
    toolSelector: {
      descriptionPlaceholder: 'شرح مختصری از هدف ابزار، به عنوان مثال، دما را برای یک مکان خاص دریافت کنید.',
      auto: 'خودکار',
      unsupportedContent: 'نسخه افزونه نصب شده این عمل را ارائه نمی دهد.',
      paramsTip1: 'پارامترهای استنتاج LLM را کنترل می کند.',
      params: 'پیکربندی استدلال',
      placeholder: 'یک ابزار را انتخاب کنید...',
      paramsTip2: 'وقتی «خودکار» خاموش باشد، از مقدار پیش فرض استفاده می شود.',
      descriptionLabel: 'توضیحات ابزار',
      title: 'ابزار افزودن',
      settings: 'تنظیمات کاربر',
      empty: 'برای افزودن ابزارها روی دکمه "+" کلیک کنید. می توانید چندین ابزار اضافه کنید.',
      toolLabel: 'ابزار',
      uninstalledTitle: 'ابزار نصب نشده است',
      uninstalledLink: 'مدیریت در پلاگین ها',
      uninstalledContent: 'این افزونه از مخزن local/GitHub نصب شده است. لطفا پس از نصب استفاده کنید.',
      unsupportedTitle: 'اکشن پشتیبانی نشده',
      unsupportedContent2: 'برای تغییر نسخه کلیک کنید.',
      toolSetting: 'تنظیمات ابزار',
    },
    endpointDeleteTip: 'حذف نقطه پایانی',
    disabled: 'غیر فعال',
    strategyNum: '{{عدد}} {{استراتژی}} شامل',
    configureApp: 'پیکربندی اپلیکیشن',
    endpoints: 'نقاط پایانی',
    endpointsDocLink: 'مشاهده سند',
    actionNum: '{{عدد}} {{اقدام}} شامل',
    endpointDisableContent: 'آیا می خواهید {{name}} را غیرفعال کنید؟',
    endpointModalTitle: 'راه اندازی اندپوینت',
    endpointsTip: 'این افزونه عملکردهای خاصی را از طریق نقاط پایانی ارائه می دهد و می توانید چندین مجموعه نقطه پایانی را برای فضای کاری فعلی پیکربندی کنید.',
    serviceOk: 'خدمات خوب',
    modelNum: '{{عدد}} مدل های گنجانده شده است',
    endpointDisableTip: 'غیرفعال کردن نقطه پایانی',
    configureModel: 'مدل را پیکربندی کنید',
    configureTool: 'ابزار پیکربندی',
    endpointsEmpty: 'برای افزودن نقطه پایانی روی دکمه "+" کلیک کنید',
    endpointModalDesc: 'پس از پیکربندی، می توان از ویژگی های ارائه شده توسط افزونه از طریق نقاط پایانی API استفاده کرد.',
    switchVersion: 'نسخه سوئیچ',
    endpointDeleteContent: 'آیا می خواهید {{name}} را حذف کنید؟',
  },
  debugInfo: {
    title: 'اشکال زدایی',
    viewDocs: 'مشاهده اسناد',
  },
  privilege: {
    everyone: 'همه',
    admins: 'مدیران',
    whoCanInstall: 'چه کسی می تواند افزونه ها را نصب و مدیریت کند؟',
    title: 'تنظیمات پلاگین',
    noone: 'هیچ',
    whoCanDebug: 'چه کسی می تواند افزونه ها را اشکال زدایی کند؟',
  },
  pluginInfoModal: {
    repository: 'مخزن',
    packageName: 'بسته',
    title: 'اطلاعات پلاگین',
    release: 'انتشار',
  },
  action: {
    pluginInfo: 'اطلاعات پلاگین',
    usedInApps: 'این افزونه در برنامه های {{num}} استفاده می شود.',
    deleteContentLeft: 'آیا می خواهید',
    checkForUpdates: 'بررسی به روزرسانی ها',
    delete: 'حذف افزونه',
    deleteContentRight: 'افزونه?',
  },
  installModal: {
    labels: {
      package: 'بسته',
      version: 'نسخهٔ',
      repository: 'مخزن',
    },
    back: 'بازگشت',
    next: 'بعدی',
    cancel: 'لغو',
    uploadingPackage: 'آپلود {{packageName}}...',
    fromTrustSource: 'لطفا مطمئن شوید که افزونه ها را فقط از <trustSource>یک منبع قابل اعتماد</trustSource> نصب می کنید.',
    readyToInstall: 'در مورد نصب افزونه زیر',
    install: 'نصب',
    pluginLoadError: 'خطای بارگذاری افزونه',
    pluginLoadErrorDesc: 'این افزونه نصب نخواهد شد',
    close: 'نزدیک',
    installFailed: 'نصب ناموفق بود',
    installFailedDesc: 'افزونه نصب شده است ناموفق است.',
    installedSuccessfullyDesc: 'این افزونه با موفقیت نصب شد.',
    dropPluginToInstall: 'بسته افزونه را برای نصب اینجا رها کنید',
    installing: 'نصب...',
    readyToInstallPackage: 'در مورد نصب افزونه زیر',
    readyToInstallPackages: 'در شرف نصب افزونه های {{num}} زیر',
    installedSuccessfully: 'نصب موفقیت آمیز بود',
    installPlugin: 'افزونه را نصب کنید',
    installComplete: 'نصب کامل شد',
    uploadFailed: 'آپلود انجام نشد',
    installWarning: 'این افزونه اجازه نصب ندارد.',
  },
  installFromGitHub: {
    installPlugin: 'افزونه را از GitHub نصب کنید',
    selectPackagePlaceholder: 'لطفا یک بسته را انتخاب کنید',
    gitHubRepo: 'مخزن GitHub',
    updatePlugin: 'افزونه را از GitHub به روز کنید',
    uploadFailed: 'آپلود انجام نشد',
    installedSuccessfully: 'نصب موفقیت آمیز بود',
    installNote: 'لطفا مطمئن شوید که افزونه ها را فقط از یک منبع قابل اعتماد نصب می کنید.',
    installFailed: 'نصب ناموفق بود',
    selectVersionPlaceholder: 'لطفا یک نسخه را انتخاب کنید',
    selectPackage: 'بسته را انتخاب کنید',
    selectVersion: 'انتخاب نسخه',
  },
  upgrade: {
    usedInApps: 'استفاده شده در برنامه های {{num}}',
    successfulTitle: 'نصب موفقیت آمیز',
    close: 'نزدیک',
    title: 'افزونه را نصب کنید',
    upgrading: 'نصب...',
    upgrade: 'نصب',
    description: 'در مورد نصب افزونه زیر',
  },
  error: {
    noReleasesFound: 'هیچ نسخه ای یافت نشد. لطفا مخزن GitHub یا URL ورودی را بررسی کنید.',
    inValidGitHubUrl: 'URL GitHub نامعتبر است. لطفا یک URL معتبر را در قالب وارد کنید: https://github.com/owner/repo',
    fetchReleasesError: 'امکان بازیابی نسخه ها وجود ندارد. لطفا بعدا دوباره امتحان کنید.',
  },
  marketplace: {
    sortOption: {
      firstReleased: 'اولین منتشر شد',
      recentlyUpdated: 'اخیرا به روز شده است',
      mostPopular: 'محبوب ترین',
      newlyReleased: 'تازه منتشر شده',
    },
    and: 'و',
    viewMore: 'بیشتر ببینید',
    moreFrom: 'اطلاعات بیشتر از Marketplace',
    pluginsResult: 'نتایج {{num}}',
    noPluginFound: 'هیچ افزونه ای یافت نشد',
    sortBy: 'شهر سیاه',
    difyMarketplace: 'بازار دیفی',
    empower: 'توسعه هوش مصنوعی خود را توانمند کنید',
    discover: 'کشف',
    verifiedTip: 'تأیید شده توسط دیفی',
    partnerTip: 'تأیید شده توسط یک شریک دیفی',
  },
  task: {
    installing: 'نصب پلاگین های {{installingLength}}، 0 انجام شد.',
    clearAll: 'پاک کردن همه',
    installedError: 'افزونه های {{errorLength}} نصب نشدند',
    installError: 'پلاگین های {{errorLength}} نصب نشدند، برای مشاهده کلیک کنید',
    installingWithSuccess: 'نصب پلاگین های {{installingLength}}، {{successLength}} موفقیت آمیز است.',
    installingWithError: 'نصب پلاگین های {{installingLength}}، {{successLength}} با موفقیت مواجه شد، {{errorLength}} ناموفق بود',
  },
  searchTools: 'ابزارهای جستجو...',
  findMoreInMarketplace: 'اطلاعات بیشتر در Marketplace',
  searchInMarketplace: 'جستجو در Marketplace',
  searchCategories: 'دسته بندی ها را جستجو کنید',
  fromMarketplace: 'از بازار',
  installPlugin: 'افزونه را نصب کنید',
  from: 'از',
  install: '{{num}} نصب می شود',
  endpointsEnabled: '{{num}} مجموعه نقاط پایانی فعال شده است',
  searchPlugins: 'جستجوی افزونه ها',
  installFrom: 'نصب از',
  installAction: 'نصب',
  allCategories: 'همه دسته بندی ها',
  search: 'جستجو',
  metadata: {
    title: 'پلاگین ها',
  },
  difyVersionNotCompatible: 'نسخه فعلی دیفی با این پلاگین سازگار نیست، لطفاً به نسخه حداقل مورد نیاز به‌روزرسانی کنید: {{minimalDifyVersion}}',
  requestAPlugin: 'درخواست یک افزونه',
  publishPlugins: 'انتشار افزونه ها',
}

export default translation
