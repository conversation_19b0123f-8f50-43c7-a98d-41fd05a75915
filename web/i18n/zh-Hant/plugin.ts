const translation = {
  category: {
    tools: '工具',
    models: '模型',
    extensions: '擴展',
    agents: '代理策略',
    all: '都',
    bundles: '束',
  },
  categorySingle: {
    model: '型',
    extension: '外延',
    agent: '代理策略',
    tool: '工具',
    bundle: '捆',
  },
  list: {
    source: {
      local: '從本地包檔安裝',
      github: '從 GitHub 安裝',
      marketplace: '從 Marketplace 安裝',
    },
    noInstalled: '未安裝插件',
    notFound: '未找到插件',
  },
  source: {
    marketplace: '市場',
    local: '本地包檔',
    github: 'GitHub 的',
  },
  detailPanel: {
    categoryTip: {
      marketplace: '從 Marketplace 安裝',
      debugging: '調試插件',
      github: '從 Github 安裝',
      local: '本地插件',
    },
    operation: {
      info: '插件資訊',
      detail: '詳',
      remove: '刪除',
      install: '安裝',
      viewDetail: '查看詳情',
      update: '更新',
      checkUpdate: '檢查更新',
    },
    toolSelector: {
      uninstalledContent: '此插件是從 local/GitHub 儲存庫安裝的。請在安裝後使用。',
      descriptionLabel: '工具描述',
      params: '推理配置',
      paramsTip2: '當 \'Automatic\' 關閉時，使用預設值。',
      descriptionPlaceholder: '工具用途的簡要描述，例如，獲取特定位置的溫度。',
      toolLabel: '工具',
      unsupportedTitle: '不支援的作',
      placeholder: '選擇工具...',
      uninstalledTitle: '未安裝工具',
      auto: '自動',
      title: '添加工具',
      unsupportedContent: '已安裝的插件版本不提供此作。',
      settings: '用戶設置',
      uninstalledLink: '在插件中管理',
      empty: '點擊 『+』 按鈕添加工具。您可以新增多個工具。',
      unsupportedContent2: '按兩下以切換版本。',
      paramsTip1: '控制 LLM 推理參數。',
      toolSetting: '工具設定',
    },
    actionNum: '{{num}}{{作}}包括',
    switchVersion: 'Switch 版本',
    strategyNum: '{{num}}{{策略}}包括',
    endpoints: '端點',
    endpointDisableTip: '禁用端點',
    endpointsTip: '此插件通過終端節點提供特定功能，您可以為當前工作區配置多個終端節點集。',
    modelNum: '{{num}}包含的型號',
    endpointsEmpty: '按兩下「+」按鈕添加端點',
    endpointDisableContent: '您想禁用 {{name}} 嗎？',
    configureApp: '配置 App',
    endpointDeleteContent: '您想刪除 {{name}} 嗎？',
    configureTool: '配置工具',
    endpointModalDesc: '配置后，即可使用插件通過 API 端點提供的功能。',
    disabled: '禁用',
    serviceOk: '服務正常',
    endpointDeleteTip: '刪除端點',
    configureModel: '配置模型',
    endpointModalTitle: '設置終端節點',
    endpointsDocLink: '查看文件',
  },
  debugInfo: {
    viewDocs: '查看文件',
    title: '調試',
  },
  privilege: {
    whoCanDebug: '誰可以調試插件？',
    whoCanInstall: '誰可以安裝和管理插件？',
    noone: '沒人',
    title: '插件首選項',
    everyone: '每個人 都',
    admins: '管理員',
  },
  pluginInfoModal: {
    repository: '存儲庫',
    release: '釋放',
    title: '插件資訊',
    packageName: '包',
  },
  action: {
    deleteContentRight: '插件？',
    deleteContentLeft: '是否要刪除',
    usedInApps: '此插件正在 {{num}} 個應用程式中使用。',
    pluginInfo: '插件資訊',
    checkForUpdates: '檢查更新',
    delete: '刪除插件',
  },
  installModal: {
    labels: {
      repository: '存儲庫',
      version: '版本',
      package: '包',
    },
    readyToInstallPackage: '即將安裝以下插件',
    back: '返回',
    installFailed: '安裝失敗',
    readyToInstallPackages: '即將安裝以下 {{num}} 個插件',
    next: '下一個',
    dropPluginToInstall: '將插件包拖放到此處進行安裝',
    pluginLoadError: '插件載入錯誤',
    installedSuccessfully: '安裝成功',
    uploadFailed: '上傳失敗',
    installFailedDesc: '插件安裝失敗。',
    fromTrustSource: '請確保您只從<trustSource>受信任的來源</trustSource>安裝插件。',
    pluginLoadErrorDesc: '此插件將不會被安裝',
    installComplete: '安裝完成',
    install: '安裝',
    installedSuccessfullyDesc: '插件已成功安裝。',
    close: '關閉',
    uploadingPackage: '正在上傳 {{packageName}}...',
    readyToInstall: '即將安裝以下插件',
    cancel: '取消',
    installPlugin: '安裝插件',
    installing: '安裝。。。',
    installWarning: '此插件不允許安裝。',
  },
  installFromGitHub: {
    gitHubRepo: 'GitHub 儲存庫',
    selectPackagePlaceholder: '請選擇一個套餐',
    installFailed: '安裝失敗',
    uploadFailed: '上傳失敗',
    selectVersion: '選擇版本',
    selectVersionPlaceholder: '請選擇一個版本',
    updatePlugin: '從 GitHub 更新插件',
    installPlugin: '從 GitHub 安裝插件',
    installedSuccessfully: '安裝成功',
    selectPackage: '選擇套餐',
    installNote: '請確保您只從受信任的來源安裝插件。',
  },
  upgrade: {
    close: '關閉',
    title: '安裝插件',
    upgrade: '安裝',
    upgrading: '安裝。。。',
    description: '即將安裝以下插件',
    usedInApps: '用於 {{num}} 個應用',
    successfulTitle: '安裝成功',
  },
  error: {
    noReleasesFound: '未找到版本。請檢查 GitHub 儲存庫或輸入 URL。',
    fetchReleasesError: '無法檢索發行版。請稍後重試。',
    inValidGitHubUrl: 'GitHub URL 無效。請輸入有效的 URL，格式為：https://github.com/owner/repo',
  },
  marketplace: {
    sortOption: {
      recentlyUpdated: '最近更新',
      newlyReleased: '新發佈',
      firstReleased: '首次發佈',
      mostPopular: '最受歡迎',
    },
    discover: '發現',
    noPluginFound: '未找到插件',
    empower: '為您的 AI 開發提供支援',
    moreFrom: '來自 Marketplace 的更多內容',
    and: '和',
    sortBy: '排序方式',
    viewMore: '查看更多',
    difyMarketplace: 'Dify 市場',
    pluginsResult: '{{num}} 個結果',
    verifiedTip: '由 Dify 驗證',
    partnerTip: '由 Dify 合作夥伴驗證',
  },
  task: {
    installingWithError: '安裝 {{installingLength}} 個插件，{{successLength}} 成功，{{errorLength}} 失敗',
    installedError: '{{errorLength}} 個插件安裝失敗',
    installError: '{{errorLength}} 個插件安裝失敗，點擊查看',
    installingWithSuccess: '安裝 {{installingLength}} 個插件，{{successLength}} 成功。',
    clearAll: '全部清除',
    installing: '安裝 {{installingLength}} 個插件，0 個完成。',
  },
  requestAPlugin: '申请插件',
  publishPlugins: '發佈插件',
  findMoreInMarketplace: '在 Marketplace 中查找更多內容',
  installPlugin: '安裝插件',
  search: '搜索',
  allCategories: '全部分類',
  from: '從',
  searchPlugins: '搜索插件',
  searchTools: '搜尋工具...',
  installAction: '安裝',
  installFrom: '安裝起始位置',
  searchInMarketplace: '在 Marketplace 中搜索',
  install: '{{num}} 次安裝',
  endpointsEnabled: '{{num}} 組已啟用端點',
  fromMarketplace: '從 Marketplace',
  searchCategories: '搜索類別',
  metadata: {
    title: '插件',
  },
  difyVersionNotCompatible: '當前的 Dify 版本與此插件不兼容，請升級至所需的最低版本：{{minimalDifyVersion}}',
}

export default translation
