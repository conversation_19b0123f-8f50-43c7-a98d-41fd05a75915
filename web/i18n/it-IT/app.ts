const translation = {
  createApp: 'CREA APP',
  types: {
    all: 'Tutti',
    chatbot: 'Chatbot',
    agent: 'Agent<PERSON>',
    workflow: 'Flusso di lavoro',
    completion: 'Completamento',
    advanced: 'Flusso di chat',
    basic: 'Basico',
  },
  duplicate: 'Duplica',
  duplicateTitle: 'Duplica App',
  export: 'Esporta DSL',
  exportFailed: 'Esportazione DSL fallita.',
  importDSL: 'Importa file DSL',
  createFromConfigFile: 'Crea da file DSL',
  deleteAppConfirmTitle: 'Eliminare questa app?',
  deleteAppConfirmContent:
    'Eliminare l\'app è irreversibile. Gli utenti non potranno più accedere alla tua app e tutte le configurazioni e i log dei prompt verranno eliminati permanentemente.',
  appDeleted: 'App eliminata',
  appDeleteFailed: 'Eliminazione dell\'app fallita',
  join: 'Unisciti alla comunità',
  communityIntro:
    'Discuta con membri del team, collaboratori e sviluppatori su diversi canali.',
  roadmap: 'Vedi la nostra roadmap',
  newApp: {
    startFromBlank: 'Crea da zero',
    startFromTemplate: 'Crea da modello',
    captionAppType: 'Che tipo di app vuoi creare?',
    chatbotDescription:
      'Crea un\'applicazione basata sulla chat. Questa app utilizza un formato domanda-e-risposta, consentendo più round di conversazione continua.',
    completionDescription:
      'Crea un\'applicazione che genera testo di alta qualità basato sui prompt, come articoli, riassunti, traduzioni e altro.',
    completionWarning: 'Questo tipo di app non sarà più supportato.',
    agentDescription:
      'Crea un Agente intelligente che può scegliere autonomamente gli strumenti per completare i compiti',
    workflowDescription:
      'Crea un\'applicazione che genera testo di alta qualità basato su flussi di lavoro orchestrati con un alto grado di personalizzazione. È adatto per utenti esperti.',
    workflowWarning: 'Attualmente in beta',
    chatbotType: 'Metodo di orchestrazione Chatbot',
    basic: 'Base',
    basicTip: 'Per principianti, può passare a Chatflow in seguito',
    basicFor: 'PER PRINCIPIANTI',
    basicDescription:
      'L\'Orchestrazione di base consente l\'orchestrazione di un\'app Chatbot utilizzando impostazioni semplici, senza la possibilità di modificare i prompt integrati. È adatta per principianti.',
    advanced: 'Chatflow',
    advancedFor: 'Per utenti avanzati',
    advancedDescription:
      'L\'Orchestrazione del flusso di lavoro orchestra i Chatbot sotto forma di flussi di lavoro, offrendo un alto grado di personalizzazione, inclusa la possibilità di modificare i prompt integrati. È adatta per utenti esperti.',
    captionName: 'Icona e nome dell\'app',
    appNamePlaceholder: 'Dai un nome alla tua app',
    captionDescription: 'Descrizione',
    appDescriptionPlaceholder: 'Inserisci la descrizione dell\'app',
    useTemplate: 'Usa questo modello',
    previewDemo: 'Anteprima demo',
    chatApp: 'Assistente',
    chatAppIntro:
      'Voglio creare un\'applicazione basata sulla chat. Questa app utilizza un formato domanda-e-risposta, consentendo più round di conversazione continua.',
    agentAssistant: 'Nuovo Agente Assistente',
    completeApp: 'Generatore di Testi',
    completeAppIntro:
      'Voglio creare un\'applicazione che genera testo di alta qualità basato sui prompt, come articoli, riassunti, traduzioni e altro.',
    showTemplates: 'Voglio scegliere da un modello',
    hideTemplates: 'Torna alla selezione della modalità',
    Create: 'Crea',
    Cancel: 'Annulla',
    nameNotEmpty: 'Il nome non può essere vuoto',
    appTemplateNotSelected: 'Seleziona un modello',
    appTypeRequired: 'Seleziona un tipo di app',
    appCreated: 'App creata',
    appCreateFailed: 'Creazione dell\'app fallita',
    Confirm: 'Confermare',
    appCreateDSLErrorPart2: 'Vuoi continuare?',
    appCreateDSLErrorPart3: 'Versione DSL dell\'applicazione corrente:',
    appCreateDSLErrorPart1: 'È stata rilevata una differenza significativa nelle versioni DSL. Forzare l\'importazione può causare il malfunzionamento dell\'applicazione.',
    caution: 'Cautela',
    appCreateDSLErrorTitle: 'Incompatibilità di versione',
    appCreateDSLWarning: 'Attenzione: la differenza di versione DSL può influire su alcune funzionalità',
    appCreateDSLErrorPart4: 'Versione DSL supportata dal sistema:',
    forBeginners: 'Tipi di app più semplici',
    noAppsFound: 'Nessuna app trovata',
    noTemplateFoundTip: 'Prova a cercare utilizzando parole chiave diverse.',
    foundResults: '{{conteggio}} Risultati',
    chatbotShortDescription: 'Chatbot basato su LLM con configurazione semplice',
    forAdvanced: 'PER UTENTI AVANZATI',
    workflowShortDescription: 'Flusso agentico per automazioni intelligenti',
    foundResult: '{{conteggio}} Risultato',
    noIdeaTip: 'Non hai idee? Dai un\'occhiata ai nostri modelli',
    completionShortDescription: 'Assistente AI per le attività di generazione del testo',
    optional: 'Opzionale',
    learnMore: 'Ulteriori informazioni',
    noTemplateFound: 'Nessun modello trovato',
    chatbotUserDescription: 'Crea rapidamente un chatbot basato su LLM con una configurazione semplice. Puoi passare a Chatflow in un secondo momento.',
    agentShortDescription: 'Agente intelligente con ragionamento e uso autonomo degli strumenti',
    completionUserDescription: 'Crea rapidamente un assistente AI per le attività di generazione di testo con una configurazione semplice.',
    advancedUserDescription: 'Flusso di lavoro con funzioni di memoria e interfaccia di chatbot.',
    workflowUserDescription: 'Crea flussi di lavoro AI autonomi visivamente con la semplicità del drag-and-drop.',
    agentUserDescription: 'Un agente intelligente in grado di ragionare in modo iterativo e di utilizzare autonomamente gli strumenti per raggiungere gli obiettivi del compito.',
    advancedShortDescription: 'Flusso di lavoro migliorato per conversazioni multiple',
    chooseAppType: 'Scegli un tipo di app',
    dropDSLToCreateApp: 'Trascina il file DSL qui per creare l\'app',
  },
  editApp: 'Modifica Info',
  editAppTitle: 'Modifica Info App',
  editDone: 'Info app aggiornata',
  editFailed: 'Aggiornamento delle info dell\'app fallito',
  iconPicker: {
    ok: 'OK',
    cancel: 'Annulla',
    emoji: 'Emoji',
    image: 'Immagine',
  },
  switch: 'Passa a Orchestrazione del flusso di lavoro',
  switchTipStart:
    'Verrà creata una nuova copia dell\'app per te, e la nuova copia passerà a Orchestrazione del flusso di lavoro. La nuova copia ',
  switchTip: 'non permetterà',
  switchTipEnd: ' di tornare a Orchestrazione di base.',
  switchLabel: 'La copia dell\'app da creare',
  removeOriginal: 'Elimina l\'app originale',
  switchStart: 'Inizia il passaggio',
  typeSelector: {
    all: 'TUTTI I Tipi',
    chatbot: 'Chatbot',
    agent: 'Agente',
    workflow: 'Flusso di lavoro',
    completion: 'Completamento',
    advanced: 'Flusso di chat',
  },
  tracing: {
    title: 'Tracciamento delle prestazioni dell\'app',
    description:
      'Configurazione di un provider LLMOps di terze parti e tracciamento delle prestazioni dell\'app.',
    config: 'Config',
    collapse: 'Comprimi',
    expand: 'Espandi',
    tracing: 'Tracciamento',
    disabled: 'Disabilitato',
    disabledTip: 'Configura prima il provider',
    enabled: 'In servizio',
    tracingDescription:
      'Cattura il contesto completo dell\'esecuzione dell\'app, incluse chiamate LLM, contesto, prompt, richieste HTTP e altro, su una piattaforma di tracciamento di terze parti.',
    configProviderTitle: {
      configured: 'Configurato',
      notConfigured: 'Configura il provider per abilitare il tracciamento',
      moreProvider: 'Altri Provider',
    },
    arize: {
      title: 'Arize',
      description: 'Osservabilità LLM di livello aziendale, valutazione online e offline, monitoraggio e sperimentazione—alimentata da OpenTelemetry. Progettata appositamente per applicazioni basate su LLM e agenti.',
    },
    phoenix: {
      title: 'Phoenix',
      description: 'Piattaforma open-source basata su OpenTelemetry per osservabilità, valutazione, ingegneria dei prompt e sperimentazione per i tuoi flussi di lavoro e agenti LLM.',
    },
    langsmith: {
      title: 'LangSmith',
      description:
        'Una piattaforma all-in-one per sviluppatori per ogni fase del ciclo di vita delle applicazioni alimentate da LLM.',
    },
    langfuse: {
      title: 'Langfuse',
      description:
        'Tracce, valutazioni, gestione dei prompt e metriche per debug e miglioramento della tua applicazione LLM.',
    },
    inUse: 'In uso',
    configProvider: {
      title: 'Config ',
      placeholder: 'Inserisci il tuo {{key}}',
      project: 'Progetto',
      publicKey: 'Chiave pubblica',
      secretKey: 'Chiave segreta',
      viewDocsLink: 'Visualizza documenti di {{key}}',
      removeConfirmTitle: 'Rimuovere la configurazione di {{key}}?',
      removeConfirmContent:
        'La configurazione attuale è in uso, rimuovendola disattiverà la funzione di Tracciamento.',
    },
    view: 'Vista',
    opik: {
      description: 'Opik è una piattaforma open source per la valutazione, il test e il monitoraggio delle applicazioni LLM.',
      title: 'Opik',
    },
    weave: {
      title: 'Intrecciare',
      description: 'Weave è una piattaforma open-source per valutare, testare e monitorare le applicazioni LLM.',
    },
  },
  answerIcon: {
    description: 'Se utilizzare l\'icona web app per la sostituzione 🤖 nell\'applicazione condivisa',
    title: 'Usa l\'icona web app per sostituire 🤖',
    descriptionInExplore: 'Se utilizzare l\'icona web app per sostituirla 🤖 in Esplora',
  },
  importFromDSLUrl: 'Dall\'URL',
  importFromDSLFile: 'Da file DSL',
  importFromDSL: 'Importazione da DSL',
  importFromDSLUrlPlaceholder: 'Incolla qui il link DSL',
  mermaid: {
    handDrawn: 'Disegnato a mano',
    classic: 'Classico',
  },
  openInExplore: 'Apri in Esplora',
  newAppFromTemplate: {
    sidebar: {
      Programming: 'Programmazione',
      Writing: 'Scrittura',
      Recommended: 'Raccomandato',
      Agent: 'Agente',
      Assistant: 'Assistente',
      HR: 'HR',
      Workflow: 'Flusso di lavoro',
    },
    byCategories: 'PER CATEGORIE',
    searchAllTemplate: 'Cerca in tutti i modelli...',
  },
  showMyCreatedAppsOnly: 'Mostra solo le mie app create',
  appSelector: {
    params: 'PARAMETRI DELL\'APP',
    noParams: 'Non sono necessari parametri',
    placeholder: 'Seleziona un\'app...',
    label: 'APP',
  },
  structOutput: {
    modelNotSupported: 'Modello non supportato',
    configure: 'Configura',
    LLMResponse: 'LLM Risposta',
    structured: 'Strutturato',
    moreFillTip: 'Mostrando un massimo di 10 livelli di annidamento',
    structuredTip: 'Le Uscite Strutturate sono una funzione che garantisce che il modello generi sempre risposte che aderiscano al tuo Schema JSON fornito.',
    notConfiguredTip: 'L\'output strutturato non è stato ancora configurato.',
    modelNotSupportedTip: 'Il modello attuale non supporta questa funzione e viene automaticamente downgradato a iniezione di prompt.',
  },
  accessItemsDescription: {
    anyone: 'Chiunque può accedere all\'app web',
    specific: 'Solo gruppi o membri specifici possono accedere all\'app web.',
    organization: 'Qualsiasi persona nell\'organizzazione può accedere all\'app web',
    external: 'Solo gli utenti esterni autenticati possono accedere all\'applicazione Web',
  },
  accessControlDialog: {
    accessItems: {
      anyone: 'Chiunque con il link',
      specific: 'Gruppi o membri specifici',
      organization: 'Solo i membri all\'interno dell\'impresa',
      external: 'Utenti esterni autenticati',
    },
    operateGroupAndMember: {
      searchPlaceholder: 'Cerca gruppi e membri',
      allMembers: 'Tutti i membri',
      expand: 'Espandere',
      noResult: 'Nessun risultato',
    },
    title: 'Controllo di accesso all\'app web',
    description: 'Imposta le autorizzazioni di accesso all\'app web',
    accessLabel: 'Chi ha accesso',
    groups_one: '{{count}} GRUPPO',
    groups_other: '{{count}} GRUPPI',
    members_one: '{{count}} MEMBRO',
    members_other: '{{count}} MEMBRI',
    noGroupsOrMembers: 'Nessun gruppo o membro selezionato',
    webAppSSONotEnabledTip: 'Si prega di contattare l\'amministratore dell\'impresa per configurare il metodo di autenticazione dell\'app web.',
    updateSuccess: 'Aggiornamento avvenuto con successo',
  },
  publishApp: {
    title: 'Chi può accedere all\'app web',
    notSet: 'Non impostato',
    notSetDesc: 'Attualmente nessuno può accedere all\'app web. Si prega di impostare i permessi.',
  },
  accessControl: 'Controllo di accesso all\'app web',
  noAccessPermission: 'Nessun permesso per accedere all\'app web',
}

export default translation
